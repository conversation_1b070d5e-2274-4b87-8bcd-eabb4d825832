# AI Auto Test

AI驱动的Playwright自动化测试VS Code插件

## 功能特性

- 🤖 **AI驱动**: 使用AI自动生成测试步骤
- 📝 **文档扫描**: 自动扫描和管理测试文档
- 🎭 **Playwright集成**: 通过MCP协议执行Playwright测试
- 📊 **实时反馈**: 提供测试执行的实时进度和结果
- ⚙️ **灵活配置**: 支持自定义AI服务和测试配置

## 安装

1. 在VS Code中打开扩展面板 (Ctrl+Shift+X)
2. 搜索 "AI Auto Test"
3. 点击安装

## 使用方法

### 1. 配置插件

1. 打开命令面板 (Ctrl+Shift+P)
2. 运行 "AI Auto Test: 打开配置面板"
3. 配置AI服务参数和测试目录

### 2. 扫描测试文档

1. 在资源管理器中右键点击测试目录
2. 选择 "AI Auto Test: 扫描测试目录"
3. 或使用命令面板运行 "AI Auto Test: 扫描测试目录"

### 3. 生成测试步骤

1. 打开测试面板
2. 选择需要生成步骤的测试文档
3. 点击 "生成步骤" 按钮

### 4. 运行测试

1. 在测试面板中选择要运行的测试
2. 点击 "运行测试" 按钮
3. 查看实时执行进度和结果

## 文件结构

插件支持以下文件类型的自动识别和分组：

- `*.md` - 测试文档（描述测试场景和期望结果）
- `*.yaml` - 步骤文档（AI生成的具体测试步骤）
- `*.result.md` - 结果文档（测试执行结果报告）

## 配置选项

### AI服务配置

- `ai-auto-test.ai.apiUrl`: AI服务API地址
- `ai-auto-test.ai.apiKey`: AI服务API密钥
- `ai-auto-test.ai.model`: 使用的AI模型
- `ai-auto-test.ai.timeout`: 请求超时时间

### 测试配置

- `ai-auto-test.test.directory`: 测试文件目录
- `ai-auto-test.test.autoScan`: 是否自动扫描目录
- `ai-auto-test.test.maxConcurrentTests`: 最大并发测试数

## 开发

### 环境要求

- Node.js 18+
- VS Code 1.85+
- TypeScript 5.8+

### 本地开发

```bash
# 安装依赖
pnpm install

# 编译TypeScript
pnpm run compile

# 监听文件变化
pnpm run watch

# 运行测试
pnpm run test

# 打包插件
pnpm run package
```

### 调试

1. 在VS Code中打开项目
2. 按F5启动调试会话
3. 在新的VS Code窗口中测试插件功能

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License

## 更新日志

### 0.0.1

- 初始版本
- 基础项目结构
- 核心组件框架
