{"name": "ai-auto-test", "displayName": "AI Auto Test", "description": "AI驱动的Playwright自动化测试插件", "version": "0.0.1", "publisher": "ai-auto-test", "engines": {"vscode": "^1.102.0"}, "categories": ["Testing", "Other"], "keywords": ["playwright", "testing", "ai", "automation", "test-generation"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "ai-auto-test.openTestPanel", "title": "打开测试面板", "category": "AI Auto Test"}, {"command": "ai-auto-test.openConfigPanel", "title": "打开配置面板", "category": "AI Auto Test"}, {"command": "ai-auto-test.scanTestDirectory", "title": "扫描测试目录", "category": "AI Auto Test"}, {"command": "ai-auto-test.generateSteps", "title": "生成测试步骤", "category": "AI Auto Test"}, {"command": "ai-auto-test.runTests", "title": "运行测试", "category": "AI Auto Test"}], "menus": {"commandPalette": [{"command": "ai-auto-test.openTestPanel"}, {"command": "ai-auto-test.openConfigPanel"}, {"command": "ai-auto-test.scanTestDirectory"}, {"command": "ai-auto-test.generateSteps"}, {"command": "ai-auto-test.runTests"}], "explorer/context": [{"command": "ai-auto-test.scanTestDirectory", "when": "explorerResourceIsFolder", "group": "ai-auto-test"}]}, "configuration": {"title": "AI Auto Test", "properties": {"ai-auto-test.ai.apiUrl": {"type": "string", "default": "https://api.openai.com/v1", "description": "AI服务API地址"}, "ai-auto-test.ai.apiKey": {"type": "string", "default": "", "description": "AI服务API密钥"}, "ai-auto-test.ai.model": {"type": "string", "default": "gpt-3.5-turbo", "description": "AI模型名称"}, "ai-auto-test.ai.timeout": {"type": "number", "default": 30000, "description": "AI服务请求超时时间（毫秒）"}, "ai-auto-test.test.directory": {"type": "string", "default": "./tests", "description": "测试文件目录路径"}, "ai-auto-test.test.autoScan": {"type": "boolean", "default": true, "description": "是否自动扫描测试目录"}, "ai-auto-test.test.maxConcurrentTests": {"type": "number", "default": 3, "description": "最大并发测试数量"}}}}, "scripts": {"vscode:prepublish": "pnpm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "pnpm run compile && pnpm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "build": "pnpm run compile"}, "devDependencies": {"@types/vscode": "^1.102.0", "@types/node": "^22.16.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@eslint/js": "^9.31.0", "eslint": "^9.31.0", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "^3.6.0"}, "dependencies": {"axios": "^1.10.0", "yaml": "^2.8.0", "chokidar": "^4.0.3"}}