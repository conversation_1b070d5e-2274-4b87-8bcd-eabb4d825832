# 设计文档

## 概述

AI Playwright测试插件是一个VS Code扩展，通过AI驱动的自然语义理解来自动化Playwright测试。插件采用模块化架构，集成MCP协议与Playwright服务通信，提供直观的用户界面进行测试管理和执行。

## 架构

### 整体架构

```mermaid
graph TB
    A[VS Code Extension Host] --> B[插件主模块]
    B --> C[配置管理器]
    B --> D[文档扫描器]
    B --> E[AI服务客户端]
    B --> F[测试执行引擎]
    B --> G[UI控制器]
    
    C --> H[配置存储]
    D --> I[文件系统]
    E --> J[OpenAI兼容API]
    F --> K[Playwright MCP服务]
    G --> L[WebView面板]
    
    subgraph "测试文件结构"
        M[测试文档 *.md]
        N[步骤文档 *.yaml]
        O[结果文档 *.result.md]
    end
    
    D --> M
    D --> N
    D --> O
```

### 技术栈

- **前端框架**: VS Code Extension API + WebView (React + vite) + pnpm
- **后端语言**: TypeScript/Node.js
- **AI集成**: OpenAI兼容API客户端
- **测试执行**: Playwright通过MCP协议
- **配置存储**: VS Code Settings API
- **文件处理**: Node.js fs模块 + YAML解析器

## 组件和接口

### 1. 配置管理器 (ConfigurationManager)

```typescript
interface AIConfiguration {
  apiUrl: string;
  apiKey: string;
  model: string;
  timeout?: number;
}

interface TestConfiguration {
  testDirectory: string;
  autoScan: boolean;
  maxConcurrentTests: number;
}

class ConfigurationManager {
  getAIConfig(): AIConfiguration;
  setAIConfig(config: AIConfiguration): Promise<void>;
  getTestConfig(): TestConfiguration;
  setTestConfig(config: TestConfiguration): Promise<void>;
  validateConfig(): Promise<boolean>;
}
```

### 2. 文档扫描器 (DocumentScanner)

```typescript
interface TestDocument {
  name: string;
  basePath: string;
  testDoc?: string;    // *.md文件路径
  stepDoc?: string;    // *.yaml文件路径
  resultDoc?: string;  // *.result.md文件路径
  status: 'complete' | 'missing-steps' | 'missing-test';
}

class DocumentScanner {
  scanDirectory(path: string): Promise<TestDocument[]>;
  watchDirectory(path: string, callback: (docs: TestDocument[]) => void): void;
  groupDocumentsByName(files: string[]): TestDocument[];
}
```

### 3. AI服务客户端 (AIServiceClient)

```typescript
interface StepGenerationRequest {
  testContent: string;
  testName: string;
  context?: string;
}

interface GeneratedStep {
  action: string;
  selector?: string;
  value?: string;
  assertion?: string;
  description: string;
}

class AIServiceClient {
  generateSteps(request: StepGenerationRequest): Promise<GeneratedStep[]>;
  validateConnection(): Promise<boolean>;
  private formatOpenAIRequest(content: string): any;
}
```

### 4. 测试执行引擎 (TestExecutionEngine)

```typescript
interface TestResult {
  testName: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  steps: StepResult[];
  error?: string;
  screenshots?: string[];
}

interface StepResult {
  step: GeneratedStep;
  status: 'passed' | 'failed';
  duration: number;
  error?: string;
  screenshot?: string;
}

class TestExecutionEngine {
  executeTest(testDoc: TestDocument): Promise<TestResult>;
  executeMultipleTests(testDocs: TestDocument[]): Promise<TestResult[]>;
  cancelExecution(testName: string): Promise<void>;
  private connectToPlaywrightMCP(): Promise<MCPConnection>;
}
```

### 5. UI控制器 (UIController)

```typescript
interface TestListItem {
  document: TestDocument;
  selected: boolean;
  running: boolean;
  lastResult?: TestResult;
}

class UIController {
  showTestPanel(): void;
  showConfigPanel(): void;
  updateTestList(items: TestListItem[]): void;
  showProgress(testName: string, progress: number): void;
  showResults(results: TestResult[]): void;
}
```

## 数据模型

### YAML步骤文档格式

```yaml
name: "登录测试"
description: "测试用户登录功能"
setup:
  - action: "navigate"
    url: "https://example.com/login"
  - action: "wait"
    selector: "#login-form"

steps:
  - action: "fill"
    selector: "#username"
    value: "testuser"
    description: "输入用户名"
    
  - action: "fill"
    selector: "#password"
    value: "password123"
    description: "输入密码"
    
  - action: "click"
    selector: "#login-button"
    description: "点击登录按钮"
    
  - action: "assert"
    selector: ".welcome-message"
    assertion: "visible"
    description: "验证欢迎消息显示"

cleanup:
  - action: "click"
    selector: "#logout"
```

### 结果文档格式

```markdown
# 测试结果报告

**测试名称**: 登录测试
**执行时间**: 2024-01-15 10:30:00
**总耗时**: 5.2秒
**状态**: ✅ 通过

## 执行步骤

### 1. 输入用户名
- **状态**: ✅ 通过
- **耗时**: 0.5秒
- **操作**: fill #username with "testuser"

### 2. 输入密码
- **状态**: ✅ 通过
- **耗时**: 0.3秒
- **操作**: fill #password with "password123"

### 3. 点击登录按钮
- **状态**: ✅ 通过
- **耗时**: 1.2秒
- **操作**: click #login-button

### 4. 验证欢迎消息显示
- **状态**: ✅ 通过
- **耗时**: 0.8秒
- **操作**: assert .welcome-message is visible

## 截图

![登录成功截图](./screenshots/login-success.png)
```

## 错误处理

### 1. AI服务错误处理

- **连接超时**: 重试机制，最多3次
- **API密钥无效**: 提示用户重新配置
- **模型不可用**: 降级到默认模型或提示用户
- **生成失败**: 记录错误日志，允许手动重试

### 2. Playwright MCP错误处理

- **MCP连接失败**: 检查MCP服务状态，提供重连选项
- **元素未找到**: 等待重试机制，记录详细错误信息
- **页面加载超时**: 配置超时时间，提供跳过选项
- **断言失败**: 记录期望值和实际值，生成对比截图

### 3. 文件系统错误处理

- **目录不存在**: 自动创建或提示用户选择有效目录
- **文件权限错误**: 提示用户检查文件权限
- **YAML解析错误**: 显示具体错误位置和修复建议
- **磁盘空间不足**: 清理临时文件，提示用户

## 测试策略

### 1. 单元测试

- **配置管理器**: 测试配置的读取、写入和验证
- **文档扫描器**: 测试文件扫描和分组逻辑
- **AI服务客户端**: 模拟API响应，测试请求格式化
- **测试执行引擎**: 模拟MCP响应，测试执行流程

### 2. 集成测试

- **AI服务集成**: 使用测试API密钥验证完整流程
- **MCP集成**: 使用本地Playwright服务测试执行
- **文件系统集成**: 测试实际文件的读写操作
- **VS Code集成**: 测试插件在VS Code环境中的行为

### 3. 端到端测试

- **完整工作流**: 从扫描文档到生成结果的完整流程
- **错误场景**: 测试各种错误情况的处理
- **性能测试**: 测试大量文档的处理性能
- **用户界面测试**: 测试WebView界面的交互

### 4. 测试数据管理

- **测试文档模板**: 创建标准的测试文档示例
- **模拟数据**: 为AI和MCP服务创建模拟响应
- **测试环境**: 隔离的测试目录和配置
- **清理机制**: 自动清理测试生成的临时文件