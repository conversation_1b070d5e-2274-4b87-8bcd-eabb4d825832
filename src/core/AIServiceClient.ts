/**
 * AI服务客户端
 * 
 * 负责与AI服务进行通信，生成测试步骤文档
 * 支持OpenAI兼容的API格式
 */

import { ErrorType, GeneratedStep, PluginError, StepGenerationRequest } from '../types';
import { ConfigurationManager } from './ConfigurationManager';

export class AIServiceClient {
    constructor(private configManager: ConfigurationManager) {}

    /**
     * 生成测试步骤
     * @param request 步骤生成请求
     * @returns 生成的步骤列表
     */
    public async generateSteps(request: StepGenerationRequest): Promise<GeneratedStep[]> {
        try {
            console.log(`开始为测试 "${request.testName}" 生成步骤...`);
            
            // TODO: 实现AI服务调用逻辑
            // 这里先返回一个模拟的步骤列表
            const mockSteps: GeneratedStep[] = [
                {
                    action: 'navigate',
                    value: 'https://example.com',
                    description: '导航到测试页面'
                },
                {
                    action: 'wait',
                    selector: '#main-content',
                    description: '等待主要内容加载'
                }
            ];

            console.log(`步骤生成完成，共生成 ${mockSteps.length} 个步骤`);
            return mockSteps;
        } catch (error) {
            console.error('生成测试步骤失败:', error);
            throw new PluginError('生成测试步骤失败', {
                type: ErrorType.AI_SERVICE_ERROR,
                originalError: error as Error
            });
        }
    }

    /**
     * 验证AI服务连接
     * @returns 连接是否有效
     */
    public async validateConnection(): Promise<boolean> {
        try {
            const config = this.configManager.getAIConfig();
            
            // 检查基本配置
            if (!config.apiUrl || !config.apiKey || !config.model) {
                return false;
            }

            // TODO: 实现实际的连接验证
            // 这里先返回true
            return true;
        } catch (error) {
            console.error('验证AI服务连接失败:', error);
            return false;
        }
    }

    /**
     * 格式化OpenAI请求
     * @param content 请求内容
     * @returns 格式化后的请求对象
     */
    private formatOpenAIRequest(content: string): Record<string, unknown> {
        const config = this.configManager.getAIConfig();
        
        return {
            model: config.model,
            messages: [
                {
                    role: 'system',
                    content: '你是一个专业的测试工程师，负责根据测试文档生成详细的Playwright测试步骤。'
                },
                {
                    role: 'user',
                    content: content
                }
            ],
            temperature: 0.1,
            max_tokens: 2000
        };
    }
}


