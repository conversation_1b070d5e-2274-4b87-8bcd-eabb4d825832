/**
 * AI Auto Test VS Code 插件主入口文件
 * 
 * 该文件是VS Code插件的主要入口点，负责：
 * 1. 插件的激活和停用
 * 2. 注册所有命令和事件处理器
 * 3. 初始化核心服务组件
 * 4. 管理插件的生命周期
 */

import * as vscode from 'vscode';
import { AIServiceClient } from './core/AIServiceClient';
import { ConfigurationManager } from './core/ConfigurationManager';
import { DocumentScanner } from './core/DocumentScanner';
import { TestExecutionEngine } from './core/TestExecutionEngine';
import { UIController } from './ui/UIController';

/**
 * 插件激活函数
 * 当插件被激活时调用，负责初始化所有组件和注册命令
 * 
 * @param context VS Code扩展上下文
 */
export function activate(context: vscode.ExtensionContext) {
    console.log('AI Auto Test 插件正在激活...');

    // 初始化核心组件
    const configManager = new ConfigurationManager();
    const documentScanner = new DocumentScanner();
    const aiServiceClient = new AIServiceClient(configManager);
    const testExecutionEngine = new TestExecutionEngine(configManager);
    const uiController = new UIController(context);

    // 暂时使用这些变量以避免ESLint警告，后续任务中会实际使用
    console.log('核心组件已初始化:', {
        aiServiceClient: !!aiServiceClient,
        testExecutionEngine: !!testExecutionEngine
    });

    // 注册命令处理器
    registerCommands(context, {
        configManager,
        documentScanner,
        aiServiceClient,
        testExecutionEngine,
        uiController
    });

    // 监听配置变化
    const configChangeDisposable = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('ai-auto-test')) {
            console.log('AI Auto Test 配置已更改，重新加载配置...');
            configManager.reloadConfiguration();
        }
    });

    // 将所有disposable添加到context中，确保插件停用时正确清理
    context.subscriptions.push(configChangeDisposable);

    console.log('AI Auto Test 插件激活完成');
}

/**
 * 插件停用函数
 * 当插件被停用时调用，负责清理资源
 */
export function deactivate() {
    console.log('AI Auto Test 插件正在停用...');
    // 这里可以添加清理逻辑，如关闭连接、保存状态等
}

/**
 * 注册所有VS Code命令
 * 
 * @param context VS Code扩展上下文
 * @param services 服务组件集合
 */
function registerCommands(
    context: vscode.ExtensionContext,
    services: {
        configManager: ConfigurationManager;
        documentScanner: DocumentScanner;
        aiServiceClient: AIServiceClient;
        testExecutionEngine: TestExecutionEngine;
        uiController: UIController;
    }
) {
    const { configManager, documentScanner, aiServiceClient, testExecutionEngine, uiController } = services;

    // 注册"打开测试面板"命令
    const openTestPanelCommand = vscode.commands.registerCommand(
        'ai-auto-test.openTestPanel',
        async () => {
            try {
                console.log('打开测试面板...');
                await uiController.showTestPanel();
                
                // 自动扫描测试目录
                const testConfig = configManager.getTestConfig();
                if (testConfig.autoScan && testConfig.testDirectory) {
                    const documents = await documentScanner.scanDirectory(testConfig.testDirectory);
                    await uiController.updateTestList(documents);
                }
            } catch (error) {
                vscode.window.showErrorMessage(`打开测试面板失败: ${error}`);
                console.error('打开测试面板失败:', error);
            }
        }
    );

    // 注册"打开配置面板"命令
    const openConfigPanelCommand = vscode.commands.registerCommand(
        'ai-auto-test.openConfigPanel',
        async () => {
            try {
                console.log('打开配置面板...');
                await uiController.showConfigPanel();
            } catch (error) {
                vscode.window.showErrorMessage(`打开配置面板失败: ${error}`);
                console.error('打开配置面板失败:', error);
            }
        }
    );

    // 注册"扫描测试目录"命令
    const scanTestDirectoryCommand = vscode.commands.registerCommand(
        'ai-auto-test.scanTestDirectory',
        async (uri?: vscode.Uri) => {
            try {
                console.log('扫描测试目录...');
                
                // 确定要扫描的目录
                let targetDirectory: string;
                if (uri && uri.fsPath) {
                    targetDirectory = uri.fsPath;
                } else {
                    const testConfig = configManager.getTestConfig();
                    targetDirectory = testConfig.testDirectory;
                }

                if (!targetDirectory) {
                    vscode.window.showWarningMessage('请先配置测试目录');
                    return;
                }

                // 执行扫描
                const documents = await documentScanner.scanDirectory(targetDirectory);
                vscode.window.showInformationMessage(`扫描完成，发现 ${documents.length} 个测试文档组`);
                
                // 更新UI
                await uiController.updateTestList(documents);
            } catch (error) {
                vscode.window.showErrorMessage(`扫描测试目录失败: ${error}`);
                console.error('扫描测试目录失败:', error);
            }
        }
    );

    // 注册"生成测试步骤"命令
    const generateStepsCommand = vscode.commands.registerCommand(
        'ai-auto-test.generateSteps',
        async () => {
            try {
                console.log('生成测试步骤...');
                // 检查AI服务连接
                const isConnected = await aiServiceClient.validateConnection();
                if (!isConnected) {
                    vscode.window.showWarningMessage('AI服务连接失败，请检查配置');
                    return;
                }
                vscode.window.showInformationMessage('测试步骤生成功能正在开发中...');
                // TODO: 实现步骤生成逻辑
            } catch (error) {
                vscode.window.showErrorMessage(`生成测试步骤失败: ${error}`);
                console.error('生成测试步骤失败:', error);
            }
        }
    );

    // 注册"运行测试"命令
    const runTestsCommand = vscode.commands.registerCommand(
        'ai-auto-test.runTests',
        async () => {
            try {
                console.log('运行测试...');
                // 检查测试执行引擎状态
                console.log('测试执行引擎已准备就绪:', !!testExecutionEngine);
                vscode.window.showInformationMessage('测试运行功能正在开发中...');
                // TODO: 实现测试运行逻辑
            } catch (error) {
                vscode.window.showErrorMessage(`运行测试失败: ${error}`);
                console.error('运行测试失败:', error);
            }
        }
    );

    // 将所有命令添加到context中
    context.subscriptions.push(
        openTestPanelCommand,
        openConfigPanelCommand,
        scanTestDirectoryCommand,
        generateStepsCommand,
        runTestsCommand
    );

    console.log('所有命令注册完成');
}
