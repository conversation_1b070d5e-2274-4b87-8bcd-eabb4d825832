hoistPattern:
  - '*'
hoistedDependencies:
  '@azu/format-text@1.0.2':
    '@azu/format-text': private
  '@azu/style-format@1.0.1':
    '@azu/style-format': private
  '@azure/abort-controller@2.1.2':
    '@azure/abort-controller': private
  '@azure/core-auth@1.10.0':
    '@azure/core-auth': private
  '@azure/core-client@1.10.0':
    '@azure/core-client': private
  '@azure/core-rest-pipeline@1.22.0':
    '@azure/core-rest-pipeline': private
  '@azure/core-tracing@1.3.0':
    '@azure/core-tracing': private
  '@azure/core-util@1.13.0':
    '@azure/core-util': private
  '@azure/identity@4.10.2':
    '@azure/identity': private
  '@azure/logger@1.3.0':
    '@azure/logger': private
  '@azure/msal-browser@4.15.0':
    '@azure/msal-browser': private
  '@azure/msal-common@15.8.1':
    '@azure/msal-common': private
  '@azure/msal-node@3.6.3':
    '@azure/msal-node': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.31.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.1':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.3':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@isaacs/balanced-match@4.0.1':
    '@isaacs/balanced-match': private
  '@isaacs/brace-expansion@5.0.0':
    '@isaacs/brace-expansion': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@secretlint/config-creator@10.2.1':
    '@secretlint/config-creator': private
  '@secretlint/config-loader@10.2.1':
    '@secretlint/config-loader': private
  '@secretlint/core@10.2.1':
    '@secretlint/core': private
  '@secretlint/formatter@10.2.1':
    '@secretlint/formatter': private
  '@secretlint/node@10.2.1':
    '@secretlint/node': private
  '@secretlint/profiler@10.2.1':
    '@secretlint/profiler': private
  '@secretlint/resolver@10.2.1':
    '@secretlint/resolver': private
  '@secretlint/secretlint-formatter-sarif@10.2.1':
    '@secretlint/secretlint-formatter-sarif': private
  '@secretlint/secretlint-rule-no-dotenv@10.2.1':
    '@secretlint/secretlint-rule-no-dotenv': private
  '@secretlint/secretlint-rule-preset-recommend@10.2.1':
    '@secretlint/secretlint-rule-preset-recommend': private
  '@secretlint/source-creator@10.2.1':
    '@secretlint/source-creator': private
  '@secretlint/types@10.2.1':
    '@secretlint/types': private
  '@sindresorhus/merge-streams@2.3.0':
    '@sindresorhus/merge-streams': private
  '@textlint/ast-node-types@15.2.0':
    '@textlint/ast-node-types': private
  '@textlint/linter-formatter@15.2.0':
    '@textlint/linter-formatter': private
  '@textlint/module-interop@15.2.0':
    '@textlint/module-interop': private
  '@textlint/resolver@15.2.0':
    '@textlint/resolver': private
  '@textlint/types@15.2.0':
    '@textlint/types': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/sarif@2.1.7':
    '@types/sarif': private
  '@typescript-eslint/project-service@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.37.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.37.0(eslint@9.31.0)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.37.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.37.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.37.0(eslint@9.31.0)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.37.0':
    '@typescript-eslint/visitor-keys': private
  '@typespec/ts-http-runtime@0.3.0':
    '@typespec/ts-http-runtime': private
  '@vscode/vsce-sign-alpine-arm64@2.0.5':
    '@vscode/vsce-sign-alpine-arm64': private
  '@vscode/vsce-sign-alpine-x64@2.0.5':
    '@vscode/vsce-sign-alpine-x64': private
  '@vscode/vsce-sign-darwin-arm64@2.0.5':
    '@vscode/vsce-sign-darwin-arm64': private
  '@vscode/vsce-sign-darwin-x64@2.0.5':
    '@vscode/vsce-sign-darwin-x64': private
  '@vscode/vsce-sign-linux-arm64@2.0.5':
    '@vscode/vsce-sign-linux-arm64': private
  '@vscode/vsce-sign-linux-arm@2.0.5':
    '@vscode/vsce-sign-linux-arm': private
  '@vscode/vsce-sign-linux-x64@2.0.5':
    '@vscode/vsce-sign-linux-x64': private
  '@vscode/vsce-sign-win32-arm64@2.0.5':
    '@vscode/vsce-sign-win32-arm64': private
  '@vscode/vsce-sign-win32-x64@2.0.5':
    '@vscode/vsce-sign-win32-x64': private
  '@vscode/vsce-sign@2.0.6':
    '@vscode/vsce-sign': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  agent-base@7.1.4:
    agent-base: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@7.0.0:
    ansi-escapes: private
  ansi-regex@6.1.0:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  astral-regex@2.0.0:
    astral-regex: private
  asynckit@0.4.0:
    asynckit: private
  azure-devops-node-api@12.5.0:
    azure-devops-node-api: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  binaryextensions@6.11.0:
    binaryextensions: private
  bl@4.1.0:
    bl: private
  boolbase@1.0.0:
    boolbase: private
  boundary@2.0.0:
    boundary: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer@5.7.1:
    buffer: private
  bundle-name@4.1.0:
    bundle-name: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  chalk@4.1.2:
    chalk: private
  cheerio-select@2.1.0:
    cheerio-select: private
  cheerio@1.1.0:
    cheerio: private
  chownr@1.1.4:
    chownr: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cockatiel@3.2.1:
    cockatiel: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@12.1.0:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  core-util-is@1.0.3:
    core-util-is: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-select@5.2.2:
    css-select: private
  css-what@6.2.2:
    css-what: private
  debug@4.4.1:
    debug: private
  decompress-response@6.0.0:
    decompress-response: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  delayed-stream@1.0.0:
    delayed-stream: private
  detect-libc@2.0.4:
    detect-libc: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  editions@6.21.0:
    editions: private
  emoji-regex@10.4.0:
    emoji-regex: private
  encoding-sniffer@0.2.1:
    encoding-sniffer: private
  end-of-stream@1.4.5:
    end-of-stream: private
  entities@4.5.0:
    entities: private
  environment@1.1.0:
    environment: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  expand-template@2.0.3:
    expand-template: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fd-slicer@1.1.0:
    fd-slicer: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.4:
    form-data: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@11.3.0:
    fs-extra: private
  function-bind@1.1.2:
    function-bind: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  github-from-package@0.0.0:
    github-from-package: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@11.0.3:
    glob: private
  globals@14.0.0:
    globals: private
  globby@14.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hosted-git-info@4.1.0:
    hosted-git-info: private
  htmlparser2@10.0.0:
    htmlparser2: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@7.0.5:
    ignore: private
  immediate@3.0.6:
    immediate: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  index-to-position@1.1.0:
    index-to-position: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-interactive@2.0.0:
    is-interactive: private
  is-number@7.0.0:
    is-number: private
  is-unicode-supported@2.1.0:
    is-unicode-supported: private
  is-wsl@3.1.0:
    is-wsl: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  istextorbinary@9.5.0:
    istextorbinary: private
  jackspeak@4.1.1:
    jackspeak: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonc-parser@3.3.1:
    jsonc-parser: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jszip@3.10.1:
    jszip: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  keytar@7.9.0:
    keytar: private
  keyv@4.5.4:
    keyv: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lie@3.3.0:
    lie: private
  linkify-it@5.0.0:
    linkify-it: private
  locate-path@6.0.0:
    locate-path: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash.truncate@4.4.2:
    lodash.truncate: private
  lodash@4.17.21:
    lodash: private
  log-symbols@6.0.0:
    log-symbols: private
  lru-cache@6.0.0:
    lru-cache: private
  markdown-it@14.1.0:
    markdown-it: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdurl@2.0.0:
    mdurl: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-function@5.0.1:
    mimic-function: private
  mimic-response@3.1.0:
    mimic-response: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  ms@2.1.3:
    ms: private
  mute-stream@0.0.8:
    mute-stream: private
  napi-build-utils@2.0.0:
    napi-build-utils: private
  natural-compare@1.4.0:
    natural-compare: private
  node-abi@3.75.0:
    node-abi: private
  node-addon-api@4.3.0:
    node-addon-api: private
  node-sarif-builder@3.2.0:
    node-sarif-builder: private
  normalize-package-data@6.0.2:
    normalize-package-data: private
  nth-check@2.1.1:
    nth-check: private
  object-inspect@1.13.4:
    object-inspect: private
  once@1.4.0:
    once: private
  onetime@7.0.0:
    onetime: private
  open@10.2.0:
    open: private
  optionator@0.9.4:
    optionator: private
  ora@8.2.0:
    ora: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@7.0.3:
    p-map: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  pako@1.0.11:
    pako: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@8.3.0:
    parse-json: private
  parse-semver@1.1.1:
    parse-semver: private
  parse5-htmlparser2-tree-adapter@7.1.0:
    parse5-htmlparser2-tree-adapter: private
  parse5-parser-stream@7.1.2:
    parse5-parser-stream: private
  parse5@7.3.0:
    parse5: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-scurry@2.0.0:
    path-scurry: private
  path-type@6.0.0:
    path-type: private
  pend@1.2.0:
    pend: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pluralize@8.0.0:
    pluralize: private
  prebuild-install@7.1.3:
    prebuild-install: private
  prelude-ls@1.2.1:
    prelude-ls: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  pump@3.0.3:
    pump: private
  punycode.js@2.3.1:
    punycode.js: private
  punycode@2.3.1:
    punycode: private
  qs@6.14.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  rc-config-loader@4.1.3:
    rc-config-loader: private
  rc@1.2.8:
    rc: private
  read-pkg@9.0.1:
    read-pkg: private
  read@1.0.7:
    read: private
  readable-stream@2.3.8:
    readable-stream: private
  readdirp@4.1.2:
    readdirp: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-from@4.0.0:
    resolve-from: private
  restore-cursor@5.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  run-applescript@7.0.0:
    run-applescript: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.1.2:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.4.1:
    sax: private
  secretlint@10.2.1:
    secretlint: private
  semver@7.7.2:
    semver: private
  setimmediate@1.0.5:
    setimmediate: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@4.0.1:
    simple-get: private
  slash@5.1.0:
    slash: private
  slice-ansi@4.0.0:
    slice-ansi: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stdin-discarder@0.2.2:
    stdin-discarder: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@7.2.0:
    string-width: private
  string_decoder@1.1.1:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  structured-source@4.0.0:
    structured-source: private
  supports-color@7.2.0:
    supports-color: private
  supports-hyperlinks@3.2.0:
    supports-hyperlinks: private
  table@6.9.0:
    table: private
  tar-fs@2.1.3:
    tar-fs: private
  tar-stream@2.2.0:
    tar-stream: private
  terminal-link@4.0.0:
    terminal-link: private
  text-table@0.2.0:
    text-table: private
  textextensions@6.11.0:
    textextensions: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  tslib@2.8.1:
    tslib: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  tunnel@0.0.6:
    tunnel: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.41.0:
    type-fest: private
  typed-rest-client@1.8.11:
    typed-rest-client: private
  uc.micro@2.1.0:
    uc.micro: private
  underscore@1.13.7:
    underscore: private
  undici-types@6.21.0:
    undici-types: private
  undici@7.11.0:
    undici: private
  unicorn-magic@0.3.0:
    unicorn-magic: private
  universalify@2.0.1:
    universalify: private
  uri-js@4.4.1:
    uri-js: private
  url-join@4.0.1:
    url-join: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@8.3.2:
    uuid: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  version-range@4.14.0:
    version-range: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  wsl-utils@0.1.0:
    wsl-utils: private
  xml2js@0.5.0:
    xml2js: private
  xmlbuilder@11.0.1:
    xmlbuilder: private
  yallist@4.0.0:
    yallist: private
  yauzl@2.10.0:
    yauzl: private
  yazl@2.5.1:
    yazl: private
  yocto-queue@0.1.0:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.6.2
pendingBuilds: []
prunedAt: Thu, 17 Jul 2025 15:14:54 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@vscode/vsce-sign-alpine-arm64@2.0.5'
  - '@vscode/vsce-sign-alpine-x64@2.0.5'
  - '@vscode/vsce-sign-darwin-x64@2.0.5'
  - '@vscode/vsce-sign-linux-arm64@2.0.5'
  - '@vscode/vsce-sign-linux-arm@2.0.5'
  - '@vscode/vsce-sign-linux-x64@2.0.5'
  - '@vscode/vsce-sign-win32-arm64@2.0.5'
  - '@vscode/vsce-sign-win32-x64@2.0.5'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
