{"name": "terminal-link", "version": "4.0.0", "description": "Create clickable links in the terminal", "license": "MIT", "repository": "sindresorhus/terminal-link", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"//test": "xo && ava && tsd", "test": "xo && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["link", "hyperlink", "url", "ansi", "escape", "terminal", "term", "console", "command-line"], "dependencies": {"ansi-escapes": "^7.0.0", "supports-hyperlinks": "^3.2.0"}, "devDependencies": {"ava": "^6.2.0", "tsd": "^0.31.2", "xo": "^0.60.0"}, "ava": {"serial": true}}