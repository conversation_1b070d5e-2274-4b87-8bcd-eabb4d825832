{"version": 3, "file": "json-schema.d.ts", "sourceRoot": "", "sources": ["../src/json-schema.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAMH;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAC3B,KAAK,GACL,OAAO,GACP,SAAS,GACT,SAAS,GACT,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,QAAQ,CAAC;AAEb;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;AAE/D,MAAM,MAAM,uBAAuB,GAC/B,gBAAgB,GAChB,iBAAiB,GACjB,eAAe,CAAC;AAEpB,MAAM,WAAW,iBAAiB;IAChC,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,CAAC;CACxC;AAKD,MAAM,WAAW,gBAAiB,SAAQ,KAAK,CAAC,uBAAuB,CAAC;CAAG;AAE3E;;;;;;;;;;;;GAYG;AACH,MAAM,MAAM,kBAAkB,GAAG,MAAM,CAAC;AAExC;;;GAGG;AACH,MAAM,MAAM,WAAW,GACnB,sBAAsB,GACtB,sBAAsB,GACtB,oBAAoB,GACpB,sBAAsB,GACtB,wBAAwB,GACxB,sBAAsB,GACtB,qBAAqB,GACrB,uBAAuB,GACvB,uBAAuB,GACvB,sBAAsB,GACtB,oBAAoB,GACpB,uBAAuB,CAAC;AAE5B,UAAU,eAAe;IACvB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC;IAEhD;;;;;;;;;;;;;;OAcG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE1B,OAAO,CAAC,EAAE,kBAAkB,GAAG,SAAS,CAAC;IAEzC;;OAEG;IACH,KAAK,CAAC,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC;IAElC;;OAEG;IACH,KAAK,CAAC,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC;IAElC;;OAEG;IACH,OAAO,CAAC,EAAE,uBAAuB,GAAG,SAAS,CAAC;IAE9C;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC;IAEtD;;;;;OAKG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEjC;;;;;;;;;;;;;;;OAeG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,SAAS,CAAC;IAExC,EAAE,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAExB;;OAEG;IACH,GAAG,CAAC,EAAE,WAAW,GAAG,SAAS,CAAC;IAE9B;;OAEG;IACH,KAAK,CAAC,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC;IAElC;;;;;;OAMG;IACH,QAAQ,CAAC,EAAE,OAAO,GAAG,MAAM,EAAE,GAAG,SAAS,CAAC;IAE1C;;;;;OAKG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE3B;;OAEG;IACH,IAAI,CAAC,EAAE,mBAAmB,GAAG,mBAAmB,EAAE,GAAG,SAAS,CAAC;CAChE;AAED,MAAM,WAAW,oBAAqB,SAAQ,eAAe;IAC3D,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,CAAC,EAAE,SAAS,CAAC;CAClB;AAED,MAAM,WAAW,sBAAuB,SAAQ,eAAe;IAC7D,KAAK,EAAE,WAAW,EAAE,CAAC;IACrB,IAAI,CAAC,EAAE,SAAS,CAAC;CAClB;AAED,MAAM,WAAW,sBAAuB,SAAQ,eAAe;IAC7D,KAAK,EAAE,WAAW,EAAE,CAAC;IACrB,IAAI,CAAC,EAAE,SAAS,CAAC;CAClB;AAED,MAAM,WAAW,sBAAuB,SAAQ,eAAe;IAC7D,KAAK,EAAE,WAAW,EAAE,CAAC;IACrB,IAAI,CAAC,EAAE,SAAS,CAAC;CAClB;AAED,MAAM,WAAW,sBACf,SAAQ,IAAI,CAAC,uBAAuB,EAAE,MAAM,GAAG,MAAM,CAAC,EACpD,IAAI,CAAC,sBAAsB,EAAE,MAAM,GAAG,MAAM,CAAC,EAC7C,IAAI,CAAC,uBAAuB,EAAE,MAAM,GAAG,MAAM,CAAC,EAC9C,IAAI,CAAC,uBAAuB,EAAE,MAAM,GAAG,MAAM,CAAC,EAC9C,IAAI,CAAC,wBAAwB,EAAE,MAAM,GAAG,MAAM,CAAC,EAC/C,IAAI,CAAC,qBAAqB,EAAE,MAAM,GAAG,MAAM,CAAC,EAC5C,IAAI,CAAC,oBAAoB,EAAE,MAAM,GAAG,MAAM,CAAC;IAC7C;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,eAAe,EAAE,CAAC;IACzB,IAAI,EAAE,mBAAmB,EAAE,CAAC;CAC7B;AAED;;GAEG;AACH,MAAM,WAAW,uBAAwB,SAAQ,eAAe;IAC9D;;;;;;;;;OASG;IACH,oBAAoB,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,SAAS,CAAC;IAEzD;;;;OAIG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,GAAG,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC;IAElE;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEnC;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEnC;;;;;;;;;;OAUG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC;IAE5D;;;;;;;;;;;;OAYG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC;IAErD,IAAI,EAAE,QAAQ,CAAC;CAChB;AAED;;GAEG;AACH,MAAM,WAAW,sBAAuB,SAAQ,eAAe;IAC7D;;;;;;;;;OASG;IACH,eAAe,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,SAAS,CAAC;IAEpD;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,EAAE,WAAW,GAAG,WAAW,EAAE,GAAG,SAAS,CAAC;IAEhD;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE9B;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE9B,IAAI,EAAE,OAAO,CAAC;IAEd;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CACnC;AAED;;GAEG;AACH,MAAM,WAAW,uBAAwB,SAAQ,eAAe;IAC9D,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;IAE5B;;;;;;;;;;OAUG;IACH,MAAM,CAAC,EACH,MAAM,GACN,WAAW,GACX,OAAO,GACP,UAAU,GACV,MAAM,GACN,MAAM,GACN,cAAc,GACd,2BAA2B,GAC3B,OAAO,GACP,uBAAuB,GACvB,MAAM,GACN,KAAK,GACL,eAAe,GACf,cAAc,GACd,KAAK,GACL,MAAM,GACN,SAAS,CAAC;IAEd;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE/B;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE/B;;;;;;;;;;;;OAYG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE7B,IAAI,EAAE,QAAQ,CAAC;CAChB;AAED;;GAEG;AACH,MAAM,WAAW,uBAAwB,SAAQ,eAAe;IAC9D;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;IAE5B;;;;;;OAMG;IACH,gBAAgB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAEvC;;;;;;OAMG;IACH,gBAAgB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAEvC;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE7B;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE7B;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEhC,IAAI,EAAE,SAAS,GAAG,QAAQ,CAAC;CAC5B;AAED;;GAEG;AACH,MAAM,WAAW,wBAAyB,SAAQ,eAAe;IAC/D;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC;IAE7B,IAAI,EAAE,SAAS,CAAC;CACjB;AAED;;GAEG;AACH,MAAM,WAAW,qBAAsB,SAAQ,eAAe;IAC5D;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;IAE1B,IAAI,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,oBAAqB,SAAQ,eAAe;IAC3D,IAAI,EAAE,KAAK,CAAC;CACb"}