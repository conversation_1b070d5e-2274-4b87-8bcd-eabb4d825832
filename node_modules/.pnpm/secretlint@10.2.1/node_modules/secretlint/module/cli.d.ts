export declare const cli: {
    input: string[];
    flags: {
        init?: boolean | undefined;
        format: string;
        output?: string | undefined;
        maskSecrets: boolean;
        secretlintrc?: string | undefined;
        secretlintignore: string;
        stdinFileName?: string | undefined;
        profile?: boolean | undefined;
        secretlintrcJSON?: string | undefined;
        locale?: string | undefined;
        color: boolean;
        terminalLink: boolean;
        cwd: string;
        debug: boolean;
        help: boolean;
        version: boolean;
        "no-config": boolean;
        "no-package": boolean;
        $schema: boolean;
        extension?: string | undefined;
        reporter?: string | undefined;
        slow?: string | undefined;
        timeout?: string | undefined;
        ui?: string | undefined;
        "watch-ignore"?: string | undefined;
        diff: boolean;
    };
};
export declare const run: (input?: string[], flags?: {
    init?: boolean | undefined;
    format: string;
    output?: string | undefined;
    maskSecrets: boolean;
    secretlintrc?: string | undefined;
    secretlintignore: string;
    stdinFileName?: string | undefined;
    profile?: boolean | undefined;
    secretlintrcJSON?: string | undefined;
    locale?: string | undefined;
    color: boolean;
    terminalLink: boolean;
    cwd: string;
    debug: boolean;
    help: boolean;
    version: boolean;
    "no-config": boolean;
    "no-package": boolean;
    $schema: boolean;
    extension?: string | undefined;
    reporter?: string | undefined;
    slow?: string | undefined;
    timeout?: string | undefined;
    ui?: string | undefined;
    "watch-ignore"?: string | undefined;
    diff: boolean;
}) => Promise<{
    exitStatus: number;
    stdout: string | null;
    stderr: Error | null;
}>;
//# sourceMappingURL=cli.d.ts.map