{"name": "secretlint", "version": "10.2.1", "description": "Secretlint CLI that scan secret/credential data.", "keywords": ["secretlint", "cli"], "homepage": "https://github.com/secretlint/secretlint/tree/master/packages/secretlint/", "bugs": {"url": "https://github.com/secretlint/secretlint/issues"}, "repository": {"type": "git", "url": "https://github.com/secretlint/secretlint.git"}, "license": "MIT", "author": "azu", "type": "module", "exports": {".": {"import": {"types": "./module/index.d.ts", "default": "./module/index.js"}, "default": "./module/index.js"}, "./cli": {"import": {"types": "./module/cli.d.ts", "default": "./module/cli.js"}, "default": "./module/cli.js"}, "./package.json": "./package.json"}, "main": "./module/index.js", "types": "./module/index.d.ts", "bin": "./bin/secretlint.js", "directories": {"lib": "lib", "test": "test"}, "files": ["bin/", "module/", "src/"], "scripts": {"build": "tsc --build", "clean": "tsc --build --clean", "prepublishOnly": "npm run clean && npm run build", "prettier": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\"", "prepublish": "npm run --if-present build", "test": "mocha", "updateSnapshot": "UPDATE_SNAPSHOT=1 npm test", "watch": "tsc --build --watch"}, "prettier": {"printWidth": 120, "singleQuote": false, "tabWidth": 4}, "dependencies": {"@secretlint/config-creator": "^10.2.1", "@secretlint/formatter": "^10.2.1", "@secretlint/node": "^10.2.1", "@secretlint/profiler": "^10.2.1", "debug": "^4.4.1", "globby": "^14.1.0", "read-pkg": "^9.0.1"}, "devDependencies": {"@secretlint/secretlint-rule-example": "^10.2.1", "@secretlint/secretlint-rule-preset-recommend": "^10.2.1", "@secretlint/types": "^10.2.1", "@types/mocha": "^10.0.10", "@types/node": "^24.0.12", "mocha": "^11.7.1", "prettier": "^2.8.8", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "packageManager": "yarn@1.22.22", "engines": {"node": ">=20.0.0"}, "gitHead": "14c3ff6ae17256eacfa7e8ac56652359d97aedf1"}