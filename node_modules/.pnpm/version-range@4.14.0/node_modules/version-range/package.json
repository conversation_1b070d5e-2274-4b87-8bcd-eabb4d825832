{"name": "version-range", "version": "4.14.0", "license": "Artistic-2.0", "description": "Check version ranges like `>=N` and `X || Y || Z` with support for Node.js, Web Browsers, Deno, and TypeScript.", "homepage": "https://github.com/bevry/version-range", "funding": "https://bevry.me/fund", "repository": {"type": "git", "url": "git+https://github.com/bevry/version-range.git"}, "bugs": {"url": "https://github.com/bevry/version-range/issues"}, "keywords": ["browser", "compare", "conditional", "deno", "deno-edition", "deno-entry", "denoland", "es2022", "es5", "export-default", "gte", "module", "node", "node.js", "nodejs", "or", "range", "semver", "typed", "types", "typescript", "version", "versions"], "badges": {"list": ["githubworkflow", "npmversion", "npmdownloads", "---", "githubsponsors", "thanksdev", "patreon", "liberapay", "buymeacoffee", "opencollective", "crypto", "paypal", "---", "discord", "twitch"], "config": {"githubWorkflow": "bevry", "githubSponsorsUsername": "bal<PERSON><PERSON>", "thanksdevGithubUsername": "bevry", "buymeacoffeeUsername": "bal<PERSON><PERSON>", "cryptoURL": "https://bevry.me/crypto", "flattrUsername": "bal<PERSON><PERSON>", "liberapayUsername": "bevry", "opencollectiveUsername": "bevry", "patreonUsername": "bevry", "paypalURL": "https://bevry.me/paypal", "wishlistURL": "https://bevry.me/wishlist", "discordServerID": "1147436445783560193", "discordServerInvite": "nQuXddV7VP", "twitchUsername": "bal<PERSON><PERSON>", "githubUsername": "bevry", "githubRepository": "version-range", "githubSlug": "bevry/version-range", "npmPackageName": "version-range"}}, "author": "<PERSON> <<EMAIL>> (https://balupton.com) (https://github.com/balupton)", "authors": ["<PERSON> <<EMAIL>> (https://balupton.com) (https://github.com/balupton): Accelerating collaborative wisdom."], "maintainers": ["<PERSON> <<EMAIL>> (https://balupton.com) (https://github.com/balupton): Accelerating collaborative wisdom."], "contributors": ["<PERSON> <<EMAIL>> (https://balupton.com) (https://github.com/balupton)"], "sponsors": ["<PERSON> (https://nesbitt.io) (https://github.com/andrew): Software engineer and researcher", "Balsa <<EMAIL>> (https://balsa.com) (https://github.com/balsa): We're Balsa, and we're building tools for builders.", "Codecov <<EMAIL>> (https://codecov.io) (https://github.com/codecov): Empower developers with tools to improve code quality and testing.", "Frontend Masters <<EMAIL>> (https://FrontendMasters.com) (https://github.com/FrontendMasters) (https://thanks.dev/d/gh/FrontendMasters): The training platform for web app engineering skills – from front-end to full-stack! 🚀", "Mr. <PERSON> <<EMAIL>> (https://mrhenry.be) (https://github.com/mrhenry)", "Poonacha Medappa (https://poonachamedappa.com) (https://github.com/km-Poonacha)", "<PERSON> <<EMAIL>> (https://github.com/<PERSON><PERSON><PERSON>)", "Sentry (https://sentry.io) (https://github.com/getsentry): Real-time crash reporting for your web apps, mobile apps, and games.", "Syntax <<EMAIL>> (https://syntax.fm) (https://github.com/syntaxfm): Syntax Podcast"], "donors": ["<PERSON> (https://nesbitt.io) (https://github.com/andrew)", "<PERSON><PERSON> Mk<PERSON>n (https://mogoni.dev) (https://github.com/Armenm)", "Balsa <<EMAIL>> (https://balsa.com) (https://github.com/balsa)", "Chad (https://opencollective.com/chad8)", "Codecov <<EMAIL>> (https://codecov.io) (https://github.com/codecov)", "dr.dimitru (https://veliovgroup.com) (https://github.com/dr-dimitru)", "<PERSON> (https://elliottditman.com) (https://github.com/elliottditman)", "entroniq (https://gitlab.com/entroniq) (https://thanks.dev/d/gl/entroniq)", "Frontend Masters <<EMAIL>> (https://FrontendMasters.com) (https://github.com/FrontendMasters) (https://thanks.dev/d/gh/FrontendMasters)", "GitHub (https://github.com/about) (https://github.com/github)", "<PERSON> (https://cryptoquick.com) (https://github.com/cryptoquick)", "<PERSON><PERSON><PERSON> (https://github.com/jlgeering) (https://opencollective.com/jlgeering) (https://twitter.com/jlgeering)", "<PERSON> (https://mdm.cc) (https://github.com/mikeumus) (https://opencollective.com/mikeumus) (https://twitter.com/mikeumus)", "<PERSON> <<EMAIL>> (https://michaelscepaniak.com) (https://github.com/hispanic)", "<PERSON> <<EMAIL>> (https://github.com/smashah) (https://thanks.dev/d/gh/smashah) (https://twitter.com/smashah)", "Mr. <PERSON> <<EMAIL>> (https://mrhenry.be) (https://github.com/mrhenry)", "Nermal <<EMAIL>> (https://arjunaditya.vercel.app) (https://github.com/nermalcat69)", "Pleo (https://pleo.io) (https://github.com/pleo-io)", "Poonacha Medappa (https://poonachamedappa.com) (https://github.com/km-Poonacha)", "<PERSON> <<EMAIL>> (https://github.com/<PERSON><PERSON><PERSON>)", "<PERSON> <<EMAIL>> (https://github.com/rdeforest)", "Sentry (https://sentry.io) (https://github.com/getsentry)", "ServieJS (https://github.com/serviejs) (https://thanks.dev/d/gh/serviejs)", "Skunk Team (https://skunk.team) (https://github.com/skunkteam)", "Syntax <<EMAIL>> (https://syntax.fm) (https://github.com/syntaxfm)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (https://github.com/WriterJ<PERSON>nBuck)"], "engines": {"node": ">=4"}, "editions": [{"description": "TypeScript source code with Import for modules", "directory": "source", "entry": "index.ts", "tags": ["source", "typescript", "import"], "engines": false}, {"description": "TypeScript compiled against ES2022 for web browsers with Import for modules", "directory": "edition-browsers", "entry": "index.js", "tags": ["compiled", "javascript", "import"], "engines": {"node": false, "browsers": "defaults"}}, {"description": "TypeScript compiled against ES5 for Node.js 4 || 6 || 8 || 10 || 12 || 14 || 16 || 18 || 20 || 21 with Require for modules", "directory": "edition-es5", "entry": "index.js", "tags": ["compiled", "javascript", "es5", "require"], "engines": {"node": "4 || 6 || 8 || 10 || 12 || 14 || 16 || 18 || 20 || 21", "browsers": false}}, {"description": "TypeScript compiled against ES2022 for Node.js 12 || 14 || 16 || 18 || 20 || 21 with Import for modules", "directory": "edition-es2022-esm", "entry": "index.js", "tags": ["compiled", "javascript", "es2022", "import"], "engines": {"node": "12 || 14 || 16 || 18 || 20 || 21", "browsers": false}}, {"description": "TypeScript compiled Types with Import for modules", "directory": "edition-types", "entry": "index.d.ts", "tags": ["compiled", "types", "import"], "engines": false}, {"description": "TypeScript source code made to be compatible with Deno", "directory": "edition-deno", "entry": "index.ts", "tags": ["typescript", "import", "deno"], "engines": {"deno": true, "browsers": true}}], "types": "edition-types/index.d.ts", "type": "module", "main": "edition-es5/index.js", "exports": {"node": {"types": "./edition-types/index.d.ts", "import": "./edition-es2022-esm/index.js", "require": "./edition-es5/index.js"}, "browser": {"types": "./edition-types/index.d.ts", "import": "./edition-browsers/index.js"}}, "deno": "edition-deno/index.ts", "browser": "edition-browsers/index.js", "module": "edition-browsers/index.js", "devDependencies": {"@bevry/json": "^2.4.0", "@types/node": "^20.10.7", "@typescript-eslint/eslint-plugin": "^6.18.0", "@typescript-eslint/parser": "^6.18.0", "assert-helpers": "^11.12.0", "eslint": "^8.56.0", "eslint-config-bevry": "^5.5.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "filedirname": "^3.4.0", "kava": "^7.9.0", "make-deno-edition": "^2.3.0", "prettier": "^3.1.1", "projectz": "^4.2.0", "typedoc": "^0.25.6", "typescript": "5.3.3", "valid-directory": "^4.9.0", "valid-module": "^2.6.0"}, "scripts": {"our:clean": "rm -rf ./docs ./edition* ./es2015 ./es5 ./out ./.next", "our:compile": "npm run our:compile:deno && npm run our:compile:edition-browsers && npm run our:compile:edition-es2022-esm && npm run our:compile:edition-es5 && npm run our:compile:edition-types", "our:compile:deno": "make-deno-edition --attempt", "our:compile:edition-browsers": "tsc --module ESNext --target ES2022 --outDir ./edition-browsers --project tsconfig.json && ( test ! -d edition-browsers/source || ( mv edition-browsers/source edition-temp && rm -rf edition-browsers && mv edition-temp edition-browsers ) )", "our:compile:edition-es2022-esm": "tsc --module ESNext --target ES2022 --outDir ./edition-es2022-esm --project tsconfig.json && ( test ! -d edition-es2022-esm/source || ( mv edition-es2022-esm/source edition-temp && rm -rf edition-es2022-esm && mv edition-temp edition-es2022-esm ) ) && printf '%s' '{\"type\": \"module\"}' > edition-es2022-esm/package.json", "our:compile:edition-es5": "tsc --module commonjs --target ES5 --outDir ./edition-es5 --project tsconfig.json && ( test ! -d edition-es5/source || ( mv edition-es5/source edition-temp && rm -rf edition-es5 && mv edition-temp edition-es5 ) ) && printf '%s' '{\"type\": \"commonjs\"}' > edition-es5/package.json", "our:compile:edition-types": "tsc --emitDeclarationOnly --declaration --declarationMap --declarationDir ./edition-types --project tsconfig.json && ( test ! -d edition-types/source || ( mv edition-types/source edition-temp && rm -rf edition-types && mv edition-temp edition-types ) )", "our:deploy": "printf '%s\n' 'no need for this project'", "our:meta": "npm run our:meta:docs && npm run our:meta:projectz", "our:meta:docs": "npm run our:meta:docs:typedoc", "our:meta:docs:typedoc": "rm -rf ./docs && typedoc --exclude '**/+(*test*|node_modules)' --excludeExternals --out ./docs ./source", "our:meta:projectz": "projectz --offline", "our:release": "npm run our:release:prepare && npm run our:release:check-changelog && npm run our:release:check-dirty && npm run our:release:tag && npm run our:release:push", "our:release:check-changelog": "cat ./HISTORY.md | grep \"v$npm_package_version\" || (printf '%s\n' \"add a changelog entry for v$npm_package_version\" && exit -1)", "our:release:check-dirty": "git diff --exit-code", "our:release:prepare": "npm run our:clean && npm run our:compile && npm run our:test && npm run our:meta", "our:release:push": "git push origin && git push origin --tags", "our:release:tag": "export MESSAGE=$(cat ./HISTORY.md | sed -n \"/## v$npm_package_version/,/##/p\" | sed 's/## //' | awk 'NR>1{print buf}{buf = $0}') && test \"$MESSAGE\" || (printf '%s\n' 'proper changelog entry not found' && exit -1) && git tag \"v$npm_package_version\" -am \"$MESSAGE\"", "our:setup": "npm run our:setup:install", "our:setup:install": "npm install", "our:test": "npm run our:verify && npm test", "our:verify": "npm run our:verify:eslint && npm run our:verify:module && npm run our:verify:prettier", "our:verify:eslint": "eslint --fix --ignore-pattern '**/*.d.ts' --ignore-pattern '**/vendor/' --ignore-pattern '**/node_modules/' --ext .mjs,.js,.jsx,.ts,.tsx ./source", "our:verify:module": "valid-module", "our:verify:prettier": "prettier --write .", "test": "node ./edition-es5/test.js"}, "boundation": {"comment": "editions depends upon version-range, as such version-range must support Node.js v4 and not use editions", "browser": true, "editionsAutoloader": false}, "eslintConfig": {"extends": ["bevry"]}, "prettier": {"semi": false, "singleQuote": true, "trailingComma": "es5", "endOfLine": "lf"}}