{"version": 3, "file": "NestedAppAuthController.mjs", "sources": ["../../src/controllers/NestedAppAuthController.ts"], "sourcesContent": [null], "names": ["AccountManager.getAccount", "AccountManager.getAllAccounts", "AccountManager.getAccountByUsername", "AccountManager.getAccountByHomeId", "AccountManager.getAccountByLocalId", "AccountManager.setActiveAccount", "AccountManager.getActiveAccount"], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;MAyDU,uBAAuB,CAAA;AA+BhC,IAAA,WAAA,CAAY,gBAA2C,EAAA;AACnD,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC;QACrD,IAAI,KAAK,KAAK,SAAS,EAAE;AACrB,YAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC5B,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;AAC3D,SAAA;;AAGD,QAAA,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,SAAS,EAAE,CAAC;;QAG3C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;;QAGhD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;;AAGtD,QAAA,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC,oBAAoB,EAAE;AACxD,cAAE,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC;cACxD,6BAA6B,CAAC;QAEpC,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;QAElD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE;AAC9D,cAAE,IAAI,mBAAmB,CACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACzB,IAAI,CAAC,MAAM,CAAC,KAAK,EACjB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,YAAY,EACjB,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAChD;cACD,6BAA6B,CACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACzB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,YAAY,CACpB,CAAC;AAER,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI,oBAAoB,CAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EACnC,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,MAAM,CACd,CAAC;;QAGF,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC;AAC5D,QAAA,IAAI,CAAC,qBAAqB,GAAG,cAAc,GAAG,cAAc,GAAG,IAAI,CAAC;KACvE;AAED;;;;AAIG;AACH,IAAA,aAAa,gBAAgB,CACzB,gBAA2C,EAAA;AAE3C,QAAA,MAAM,UAAU,GAAG,IAAI,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;AACjE,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;KACtC;AAED;;;AAGG;IACH,MAAM,UAAU,CACZ,OAAsC;;IAEtC,QAAkB,EAAA;QAElB,MAAM,iBAAiB,GAAG,OAAO,EAAE,aAAa,IAAI,aAAa,EAAE,CAAC;QACpE,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;AACxD,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC5B;AAED;;;;AAIG;AACK,IAAA,kBAAkB,CAMxB,OAAU,EAAA;QACR,IAAI,OAAO,EAAE,aAAa,EAAE;AACxB,YAAA,OAAO,OAAO,CAAC;AAClB,SAAA;QACD,OAAO;AACH,YAAA,GAAG,OAAO;AACV,YAAA,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;SACpD,CAAC;KACL;AAED;;;;AAIG;IACK,MAAM,uBAAuB,CACjC,OAAuC,EAAA;QAEvC,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;AAEtD,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,mBAAmB,EAC7B,eAAe,CAAC,KAAK,EACrB,YAAY,CACf,CAAC;AAEF,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAC9D,iBAAiB,CAAC,iBAAiB,EACnC,YAAY,CAAC,aAAa,CAC7B,CAAC;QAEF,kBAAkB,EAAE,GAAG,CAAC,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC,CAAC;QAExD,IAAI;YACA,MAAM,UAAU,GACZ,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AAC9D,YAAA,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACvD,UAAU,CACb,CAAC;AACF,YAAA,MAAM,MAAM,GAAyB;gBACjC,GAAG,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAC7C,UAAU,EACV,QAAQ,EACR,YAAY,CACf;aACJ,CAAC;;YAGF,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;;YAGzC,IAAI,CAAC,qBAAqB,GAAG;AACzB,gBAAA,aAAa,EAAE,MAAM,CAAC,OAAO,CAAC,aAAa;AAC3C,gBAAA,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,WAAW;AACvC,gBAAA,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ;aACpC,CAAC;AAEF,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,qBAAqB,EAC/B,eAAe,CAAC,KAAK,EACrB,MAAM,CACT,CAAC;YAEF,kBAAkB,CAAC,GAAG,CAAC;AACnB,gBAAA,eAAe,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM;AAC1C,gBAAA,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM;AACrC,aAAA,CAAC,CAAC;YAEH,kBAAkB,CAAC,GAAG,CAAC;AACnB,gBAAA,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM,CAAC,SAAS;AAC9B,aAAA,CAAC,CAAC;AAEH,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,KAAK,GACP,CAAC,YAAY,SAAS;AAClB,kBAAE,CAAC;kBACD,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;AACvD,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,qBAAqB,EAC/B,eAAe,CAAC,KAAK,EACrB,IAAI,EACJ,CAAe,CAClB,CAAC;YAEF,kBAAkB,CAAC,GAAG,CAClB;AACI,gBAAA,OAAO,EAAE,KAAK;aACjB,EACD,CAAC,CACJ,CAAC;AAEF,YAAA,MAAM,KAAK,CAAC;AACf,SAAA;KACJ;AAED;;;;AAIG;IACK,MAAM,0BAA0B,CACpC,OAAsB,EAAA;QAEtB,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,mBAAmB,EAC7B,eAAe,CAAC,MAAM,EACtB,YAAY,CACf,CAAC;;QAGF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;AAC9D,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,qBAAqB,EAC/B,eAAe,CAAC,MAAM,EACtB,MAAM,CACT,CAAC;AACF,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;;AAGD,QAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAChE,iBAAiB,CAAC,SAAS,EAC3B,YAAY,CAAC,aAAa,CAC7B,CAAC;QAEF,oBAAoB,EAAE,SAAS,CAAC;AAC5B,YAAA,qBAAqB,EAAE,CAAC;AAC3B,SAAA,CAAC,CAAC;QAEH,oBAAoB,EAAE,GAAG,CAAC;AACtB,YAAA,oBAAoB,EAAE,IAAI;AAC7B,SAAA,CAAC,CAAC;QAEH,IAAI;YACA,MAAM,UAAU,GACZ,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AAC9D,YAAA,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAEnE,YAAA,MAAM,MAAM,GACR,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAC1C,UAAU,EACV,QAAQ,EACR,YAAY,CACf,CAAC;;YAGN,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;;YAGzC,IAAI,CAAC,qBAAqB,GAAG;AACzB,gBAAA,aAAa,EAAE,MAAM,CAAC,OAAO,CAAC,aAAa;AAC3C,gBAAA,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,WAAW;AACvC,gBAAA,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ;aACpC,CAAC;AAEF,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,qBAAqB,EAC/B,eAAe,CAAC,MAAM,EACtB,MAAM,CACT,CAAC;YACF,oBAAoB,EAAE,GAAG,CAAC;AACtB,gBAAA,eAAe,EAAE,MAAM,CAAC,WAAW,CAAC,MAAM;AAC1C,gBAAA,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM;AACrC,aAAA,CAAC,CAAC;YACH,oBAAoB,EAAE,GAAG,CAAC;AACtB,gBAAA,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM,CAAC,SAAS;AAC9B,aAAA,CAAC,CAAC;AACH,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,KAAK,GACP,CAAC,YAAY,SAAS;AAClB,kBAAE,CAAC;kBACD,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;AACvD,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,qBAAqB,EAC/B,eAAe,CAAC,MAAM,EACtB,IAAI,EACJ,CAAe,CAClB,CAAC;YACF,oBAAoB,EAAE,GAAG,CACrB;AACI,gBAAA,OAAO,EAAE,KAAK;aACjB,EACD,CAAC,CACJ,CAAC;AACF,YAAA,MAAM,KAAK,CAAC;AACf,SAAA;KACJ;AAED;;;;AAIG;IACK,MAAM,qBAAqB,CAC/B,OAAsB,EAAA;AAEtB,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAC1D,iBAAiB,CAAC,kBAAkB,EACpC,OAAO,CAAC,aAAa,CACxB,CAAC;QAEF,cAAc,EAAE,GAAG,CAAC;AAChB,YAAA,oBAAoB,EAAE,IAAI;AAC7B,SAAA,CAAC,CAAC;;QAGH,IAAI,OAAO,CAAC,MAAM,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,0DAA0D,CAC7D,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;;QAGD,IAAI,OAAO,CAAC,YAAY,EAAE;AACtB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oDAAoD,CACvD,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;;QAGD,IAAI,MAAM,GAAgC,IAAI,CAAC;AAC/C,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;AAC5B,YAAA,OAAO,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,OAAO,CAAC;AACzD,SAAA;QAED,QAAQ,OAAO,CAAC,iBAAiB;YAC7B,KAAK,iBAAiB,CAAC,OAAO,CAAC;YAC/B,KAAK,iBAAiB,CAAC,WAAW,CAAC;YACnC,KAAK,iBAAiB,CAAC,0BAA0B;gBAC7C,MAAM,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;gBAC3D,MAAM;AACV,YAAA;AACI,gBAAA,OAAO,IAAI,CAAC;AACnB,SAAA;AAED,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,qBAAqB,EAC/B,eAAe,CAAC,MAAM,EACtB,MAAM,CACT,CAAC;YACF,cAAc,EAAE,GAAG,CAAC;AAChB,gBAAA,eAAe,EAAE,MAAM,EAAE,WAAW,CAAC,MAAM;AAC3C,gBAAA,WAAW,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM;AACtC,aAAA,CAAC,CAAC;YACH,cAAc,EAAE,GAAG,CAAC;AAChB,gBAAA,OAAO,EAAE,IAAI;AAChB,aAAA,CAAC,CAAC;AACH,YAAA,OAAO,MAAM,CAAC;AACjB,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oFAAoF,CACvF,CAAC;AAEF,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CACvB,SAAS,CAAC,qBAAqB,EAC/B,eAAe,CAAC,MAAM,EACtB,IAAI,CACP,CAAC;QACF,cAAc,EAAE,GAAG,CAAC;AAChB,YAAA,OAAO,EAAE,KAAK;AACjB,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;AAIG;IACK,MAAM,6BAA6B,CACvC,OAAsB,EAAA;;AAGtB,QAAA,MAAM,cAAc,GAChB,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAC,qBAAqB,CAAC;QACvE,IAAI,cAAc,GAAuB,IAAI,CAAC;AAC9C,QAAA,MAAM,aAAa,GACf,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;AAChE,QAAA,IAAI,cAAc,EAAE;AAChB,YAAA,cAAc,GAAGA,UAAyB,CACtC,cAAc,EACd,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,EACnB,aAAa,CAChB,CAAC;AACL,SAAA;;QAGD,IAAI,CAAC,cAAc,EAAE;AACjB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,mDAAmD,CACtD,CAAC;AACF,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAChC,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4DAA4D,CAC/D,CAAC;AAEF,QAAA,MAAM,WAAW,GAAoB;AACjC,YAAA,GAAG,OAAO;YACV,aAAa,EACT,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;AAC/D,YAAA,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,cAAc,CAAC,WAAW;AAC1D,YAAA,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM;kBACxB,OAAO,CAAC,MAAM;AAChB,kBAAE,CAAC,GAAG,mBAAmB,CAAC;SACjC,CAAC;;QAGF,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,CAAC;AACrD,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CACxD,cAAc,EACd,WAAW,EACX,SAAS,EACT,cAAc,CAAC,QAAQ,CAC1B,CAAC;;QAGF,IAAI,CAAC,iBAAiB,EAAE;AACpB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;AACpD,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAChC,SAAA;AAAM,aAAA,IACH,SAAS,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,QAAQ,CAAC;AACxD,YAAA,SAAS,CAAC,cAAc,CACpB,iBAAiB,CAAC,SAAS,EAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,yBAAyB,CAC/C,EACH;AACE,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;AACvD,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAChC,SAAA;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAChD,cAAc,EACd,WAAW,CAAC,aAAa,EACzB,SAAS,EACT,cAAc,CAAC,QAAQ,EACvB,IAAI,CAAC,iBAAiB,CACzB,CAAC;QAEF,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;AAChD,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAChC,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,+BAA+B,CAC5D,cAAc,EACd,aAAa,EACb,iBAAiB,EACjB,WAAW,EACX,WAAW,CAAC,aAAa,CAC5B,CAAC;KACL;AAED;;;;AAIG;IACH,MAAM,iBAAiB,CACnB,OAAqB,EAAA;AAErB,QAAA,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;KAChD;AAED;;;AAGG;;AAEH,IAAA,oBAAoB,CAAC,OAAwB,EAAA;AACzC,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;AAED;;;;AAIG;IACH,MAAM,kBAAkB,CACpB,aAA4B,EAAA;AAE5B,QAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC;KACzD;AAED;;;AAGG;;IAEH,kBAAkB,CACd,OAAiC;;AAEjC,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;AAED;;;;;AAKG;AACH,IAAA,kBAAkB,CACd,OAakB,EAClB,KAAY;AACZ,IAAA,SAA8B;;AAE9B,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;AAED;;;;AAIG;IACH,0BAA0B,CACtB,aAAsC;AACtC,IAAA,aAA4B;;AAE5B,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;AAED;;;;AAIG;IACH,gBAAgB,CACZ,QAA+B,EAC/B,UAA6B,EAAA;QAE7B,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;KACnE;AAED;;;AAGG;AACH,IAAA,mBAAmB,CAAC,UAAkB,EAAA;AAClC,QAAA,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;KACrD;;AAGD,IAAA,sBAAsB,CAAC,QAAqC,EAAA;AACxD,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;;AAGD,IAAA,yBAAyB,CAAC,UAAkB,EAAA;AACxC,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;IAED,0BAA0B,GAAA;AACtB,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;IAED,2BAA2B,GAAA;AACvB,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;;AAID;;;;AAIG;AACH,IAAA,cAAc,CAAC,aAA6B,EAAA;QACxC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACzD,OAAOC,cAA6B,CAChC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,YAAY,EAAE,EACnB,aAAa,EACb,aAAa,CAChB,CAAC;KACL;AAED;;;;AAIG;AACH,IAAA,UAAU,CAAC,aAA4B,EAAA;QACnC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;AACzD,QAAA,OAAOD,UAAyB,CAC5B,aAAa,EACb,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,EACnB,aAAa,CAChB,CAAC;KACL;AAED;;;;;;;AAOG;AACH,IAAA,oBAAoB,CAAC,QAAgB,EAAA;QACjC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;AACzD,QAAA,OAAOE,oBAAmC,CACtC,QAAQ,EACR,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,EACnB,aAAa,CAChB,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,kBAAkB,CAAC,aAAqB,EAAA;QACpC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;AACzD,QAAA,OAAOC,kBAAiC,CACpC,aAAa,EACb,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,EACnB,aAAa,CAChB,CAAC;KACL;AAED;;;;;;AAMG;AACH,IAAA,mBAAmB,CAAC,cAAsB,EAAA;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;AACzD,QAAA,OAAOC,mBAAkC,CACrC,cAAc,EACd,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,EACnB,aAAa,CAChB,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,OAA2B,EAAA;AACxC;;;AAGG;QACH,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;AACzD,QAAA,OAAOC,gBAA+B,CAClC,OAAO,EACP,IAAI,CAAC,cAAc,EACnB,aAAa,CAChB,CAAC;KACL;AAED;;AAEG;IACH,gBAAgB,GAAA;QACZ,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACzD,OAAOC,gBAA+B,CAClC,IAAI,CAAC,cAAc,EACnB,aAAa,CAChB,CAAC;KACL;;IAID,qBAAqB,CACjB,IAAyB;;AAEzB,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KAChC;IACD,UAAU,CACN,OAAkC;;QAElC,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,IAAI,eAAe,CAAC,CAAC;KACnE;;AAED,IAAA,aAAa,CAAC,OAAqC,EAAA;AAC/C,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;;AAED,IAAA,MAAM,CAAC,aAA6C,EAAA;AAChD,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;IACD,cAAc,CACV,aAA6C;;AAE7C,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;IACD,WAAW,CACP,aAAkD;;AAElD,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;IACD,SAAS;;IAEL,OAUC,EAAA;AAED,QAAA,OAAO,IAAI,CAAC,0BAA0B,CAAC,OAAwB,CAAC,CAAC;KACpE;IACD,aAAa,GAAA;AACT,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;AAED;;AAEG;IACI,SAAS,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,MAAc,EAAA;AACpB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;;IAGD,wBAAwB,CAAC,GAAe,EAAE,OAAe,EAAA;AACrD;;;AAGG;QACH,OAAO;KACV;;AAGD,IAAA,mBAAmB,CAAC,gBAAmC,EAAA;AACnD,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yDAAyD,CAC5D,CAAC;KACL;IAED,gBAAgB,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;IAED,YAAY,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;KACvD;IAED,gBAAgB,GAAA;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;KAC7B;IAED,oBAAoB,GAAA;AAChB,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;IAED,mBAAmB,GAAA;AACf,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;;IAGD,MAAM,UAAU,CAAC,aAAiC,EAAA;AAC9C,QAAA,MAAM,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;KACrD;AAED,IAAA,MAAM,YAAY,CACd,MAA4B,EAC5B,OAIkB,EAAA;AAElB,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAE3C,QAAA,MAAM,aAAa,GAAG,aAAa,CAAC,qBAAqB,CACrD,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,WAAW,CACrB,CAAC;AACF,QAAA,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAChC,aAAa,EACb,MAAM,CAAC,aAAa,CACvB,CAAC;QACF,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KAC5D;AACJ;;;;"}