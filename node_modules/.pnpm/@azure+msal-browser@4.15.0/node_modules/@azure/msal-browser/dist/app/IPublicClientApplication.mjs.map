{"version": 3, "file": "IPublicClientApplication.mjs", "sources": ["../../src/app/IPublicClientApplication.ts"], "sourcesContent": [null], "names": ["BrowserConfigurationAuthErrorCodes.stubbedPublicClientApplicationCalled"], "mappings": ";;;;;AAAA;;;AAGG;AAiFU,MAAA,8BAA8B,GAA6B;IACpE,UAAU,EAAE,MAAK;QACb,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,iBAAiB,EAAE,MAAK;QACpB,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,oBAAoB,EAAE,MAAK;QACvB,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,kBAAkB,EAAE,MAAK;QACrB,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,kBAAkB,EAAE,MAAK;QACrB,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,cAAc,EAAE,MAAK;AACjB,QAAA,OAAO,EAAE,CAAC;KACb;IACD,UAAU,EAAE,MAAK;AACb,QAAA,OAAO,IAAI,CAAC;KACf;IACD,kBAAkB,EAAE,MAAK;AACrB,QAAA,OAAO,IAAI,CAAC;KACf;IACD,oBAAoB,EAAE,MAAK;AACvB,QAAA,OAAO,IAAI,CAAC;KACf;IACD,mBAAmB,EAAE,MAAK;AACtB,QAAA,OAAO,IAAI,CAAC;KACf;IACD,qBAAqB,EAAE,MAAK;QACxB,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,UAAU,EAAE,MAAK;QACb,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,aAAa,EAAE,MAAK;QAChB,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,MAAM,EAAE,MAAK;QACT,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,cAAc,EAAE,MAAK;QACjB,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,WAAW,EAAE,MAAK;QACd,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,SAAS,EAAE,MAAK;QACZ,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,gBAAgB,EAAE,MAAK;AACnB,QAAA,OAAO,IAAI,CAAC;KACf;IACD,mBAAmB,EAAE,MAAK;QACtB,OAAO;KACV;IACD,sBAAsB,EAAE,MAAK;AACzB,QAAA,OAAO,EAAE,CAAC;KACb;IACD,yBAAyB,EAAE,MAAK;AAC5B,QAAA,OAAO,KAAK,CAAC;KAChB;IACD,0BAA0B,EAAE,MAAK;QAC7B,OAAO;KACV;IACD,2BAA2B,EAAE,MAAK;QAC9B,OAAO;KACV;IACD,aAAa,EAAE,MAAK;AAChB,QAAA,MAAM,mCAAmC,CACrCA,oCAAuE,CAC1E,CAAC;KACL;IACD,SAAS,EAAE,MAAK;AACZ,QAAA,MAAM,mCAAmC,CACrCA,oCAAuE,CAC1E,CAAC;KACL;IACD,SAAS,EAAE,MAAK;QACZ,OAAO;KACV;IACD,gBAAgB,EAAE,MAAK;QACnB,OAAO;KACV;IACD,gBAAgB,EAAE,MAAK;AACnB,QAAA,OAAO,IAAI,CAAC;KACf;IACD,wBAAwB,EAAE,MAAK;QAC3B,OAAO;KACV;IACD,mBAAmB,EAAE,MAAK;QACtB,OAAO;KACV;IACD,gBAAgB,EAAE,MAAK;AACnB,QAAA,MAAM,mCAAmC,CACrCA,oCAAuE,CAC1E,CAAC;KACL;IACD,YAAY,EAAE,MAAK;QACf,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;IACD,UAAU,EAAE,MAAK;QACb,OAAO,OAAO,CAAC,MAAM,CACjB,mCAAmC,CAC/BA,oCAAuE,CAC1E,CACJ,CAAC;KACL;;;;;"}