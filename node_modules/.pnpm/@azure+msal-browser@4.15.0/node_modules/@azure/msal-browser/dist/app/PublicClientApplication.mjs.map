{"version": 3, "file": "PublicClientApplication.mjs", "sources": ["../../src/app/PublicClientApplication.ts"], "sourcesContent": [null], "names": ["ControllerFactory.createV3Controller"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAmCH;;;AAGG;MACU,uBAAuB,CAAA;AAIhC;;;;AAIG;AACI,IAAA,aAAa,6BAA6B,CAC7C,aAA4B,EAAA;QAE5B,MAAM,UAAU,GAAG,MAAMA,kBAAoC,CACzD,aAAa,CAChB,CAAC;QACF,MAAM,GAAG,GAAG,IAAI,uBAAuB,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;AAEnE,QAAA,OAAO,GAAG,CAAC;KACd;AAED;;;;;;;;;;;;;;;;;;;;;AAqBG;IACH,WAAmB,CAAA,aAA4B,EAAE,UAAwB,EAAA;QAxC/D,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;AAyChC,QAAA,IAAI,CAAC,UAAU;YACX,UAAU;gBACV,IAAI,kBAAkB,CAAC,IAAI,wBAAwB,CAAC,aAAa,CAAC,CAAC,CAAC;KAC3E;AAED;;;AAGG;IACH,MAAM,UAAU,CAAC,OAAsC,EAAA;AACnD,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC7D;AAED;;;;;;AAMG;IACH,MAAM,iBAAiB,CACnB,OAAqB,EAAA;QAErB,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;KACrD;AAED;;;;;;;;AAQG;AACH,IAAA,oBAAoB,CAAC,OAAwB,EAAA;QACzC,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;KACxD;AAED;;;;;AAKG;AACH,IAAA,kBAAkB,CACd,aAA4B,EAAA;QAE5B,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;KAC5D;AAED;;;;;;;;;AASG;AACH,IAAA,kBAAkB,CACd,OAAiC,EAAA;QAEjC,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;KACtD;AAED;;;;AAIG;IACH,gBAAgB,CACZ,QAA+B,EAC/B,UAA6B,EAAA;QAE7B,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;KACjE;AAED;;;AAGG;AACH,IAAA,mBAAmB,CAAC,UAAkB,EAAA;QAClC,OAAO,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;KAC1D;AAED;;;;;AAKG;AACH,IAAA,sBAAsB,CAAC,QAAqC,EAAA;QACxD,OAAO,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;KAC3D;AAED;;;;;AAKG;AACH,IAAA,yBAAyB,CAAC,UAAkB,EAAA;QACxC,OAAO,IAAI,CAAC,UAAU,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;KAChE;AAED;;AAEG;IACH,0BAA0B,GAAA;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,0BAA0B,EAAE,CAAC;KAChD;AAED;;AAEG;IACH,2BAA2B,GAAA;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,2BAA2B,EAAE,CAAC;KACjD;AAED;;;;AAIG;AACH,IAAA,UAAU,CAAC,aAA4B,EAAA;QACnC,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;KACpD;AAED;;;;;;;AAOG;AACH,IAAA,kBAAkB,CAAC,aAAqB,EAAA;QACpC,OAAO,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;KAC5D;AAED;;;;;;;AAOG;AACH,IAAA,mBAAmB,CAAC,OAAe,EAAA;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;KACvD;AAED;;;;;;;;AAQG;AACH,IAAA,oBAAoB,CAAC,QAAgB,EAAA;QACjC,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;KACzD;AAED;;;;AAIG;AACH,IAAA,cAAc,CAAC,aAA6B,EAAA;QACxC,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;KACxD;AAED;;;;;;AAMG;AACH,IAAA,qBAAqB,CACjB,IAAyB,EAAA;QAEzB,OAAO,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;KACtD;AAED;;;;;;AAMG;AACH,IAAA,UAAU,CACN,OAAkC,EAAA;QAElC,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KAC9C;AAED;;;;;;;;AAQG;AACH,IAAA,aAAa,CAAC,OAAqC,EAAA;QAC/C,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;KACjD;AAED;;;;AAIG;AACH,IAAA,MAAM,CAAC,aAAiC,EAAA;QACpC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;KAChD;AAED;;;;AAIG;AACH,IAAA,cAAc,CAAC,aAAiC,EAAA;QAC5C,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;KACxD;AAED;;;AAGG;AACH,IAAA,WAAW,CAAC,aAAsC,EAAA;QAC9C,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;KACrD;AAED;;;;;;;;;;;;;;AAcG;AACH,IAAA,SAAS,CAAC,OAAyB,EAAA;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;KAC7C;AAED;;AAEG;IACH,aAAa,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;KAC1C;AAED;;AAEG;IACH,SAAS,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;KACtC;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,MAAc,EAAA;AACpB,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KACrC;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,OAA2B,EAAA;AACxC,QAAA,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;KAC7C;AAED;;AAEG;IACH,gBAAgB,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;KAC7C;AAED;;;;AAIG;IACH,wBAAwB,CAAC,GAAe,EAAE,OAAe,EAAA;QACrD,OAAO,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KACjE;AAED;;;AAGG;AACH,IAAA,mBAAmB,CAAC,gBAAmC,EAAA;AACnD,QAAA,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;KACzD;AAED;;;AAGG;IACH,gBAAgB,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;KAC7C;AAED;;;;;AAKG;AACH,IAAA,MAAM,YAAY,CACd,MAA4B,EAC5B,OAIkB,EAAA;QAElB,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KACxD;AAED;;;AAGG;AACH,IAAA,UAAU,CAAC,aAAiC,EAAA;QACxC,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;KACpD;AACJ,CAAA;AAED;;;;;;;AAOG;AACI,eAAe,qCAAqC,CACvD,aAA4B,EAAA;AAE5B,IAAA,MAAM,aAAa,GAAG,IAAI,yBAAyB,CAAC,aAAa,CAAC,CAAC;AACnE,IAAA,MAAM,aAAa,CAAC,UAAU,EAAE,CAAC;AAEjC,IAAA,IAAI,aAAa,CAAC,WAAW,EAAE,EAAE;AAC7B,QAAA,MAAM,UAAU,GAAG,IAAI,uBAAuB,CAAC,aAAa,CAAC,CAAC;QAC9D,MAAM,WAAW,GAAG,IAAI,uBAAuB,CAC3C,aAAa,EACb,UAAU,CACb,CAAC;AACF,QAAA,MAAM,WAAW,CAAC,UAAU,EAAE,CAAC;AAC/B,QAAA,OAAO,WAAW,CAAC;AACtB,KAAA;AAED,IAAA,OAAO,qCAAqC,CAAC,aAAa,CAAC,CAAC;AAChE,CAAC;AAED;;;;;;AAMG;AACI,eAAe,qCAAqC,CACvD,aAA4B,EAAA;AAE5B,IAAA,MAAM,GAAG,GAAG,IAAI,uBAAuB,CAAC,aAAa,CAAC,CAAC;AACvD,IAAA,MAAM,GAAG,CAAC,UAAU,EAAE,CAAC;AACvB,IAAA,OAAO,GAAG,CAAC;AACf;;;;"}