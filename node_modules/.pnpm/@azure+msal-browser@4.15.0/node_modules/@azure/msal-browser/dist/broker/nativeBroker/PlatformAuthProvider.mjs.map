{"version": 3, "file": "PlatformAuthProvider.mjs", "sources": ["../../../src/broker/nativeBroker/PlatformAuthProvider.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;;;;;AAAA;;;AAGG;AAuBH;;;;;AAKG;AACI,eAAe,yBAAyB,CAC3C,aAA6B,EAC7B,UAA+B,EAC/B,aAAsB,EAAA;AAEtB,IAAA,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAE9D,IAAA,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;AAEjD,IAAA,MAAM,iBAAiB,GAAG,UAAU,IAAI,IAAI,qBAAqB,EAAE,CAAC;AAEpE,IAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC/B,QAAA,MAAM,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;AAClE,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;AAED,IAAA,OAAO,CAAC,EAAE,MAAM,uBAAuB,CACnC,MAAM,EACN,iBAAiB,EACjB,aAAa,IAAI,aAAa,EAAE,CACnC,CAAC,CAAC;AACP,CAAC;AAEM,eAAe,uBAAuB,CACzC,MAAc,EACd,iBAAqC,EACrC,aAAqB,EACrB,4BAAqC,EAAA;AAErC,IAAA,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,aAAa,CAAC,CAAC;AAE9D,IAAA,MAAM,8BAA8B,GAAG,2BAA2B,EAAE,CAAC;IAErE,MAAM,CAAC,KAAK,CACR,gDAAgD;AAC5C,QAAA,8BAA8B,CACrC,CAAC;AACF,IAAA,IAAI,oBAAsD,CAAC;IAC3D,IAAI;AACA,QAAA,IAAI,8BAA8B,EAAE;;AAEhC,YAAA,oBAAoB,GAAG,MAAM,sBAAsB,CAAC,cAAc,CAC9D,MAAM,EACN,iBAAiB,EACjB,aAAa,CAChB,CAAC;AACL,SAAA;QACD,IAAI,CAAC,oBAAoB,EAAE;AACvB,YAAA,MAAM,CAAC,KAAK,CACR,iEAAiE,CACpE,CAAC;AACF;;;AAGG;YACH,oBAAoB;AAChB,gBAAA,MAAM,4BAA4B,CAAC,cAAc,CAC7C,MAAM,EACN,4BAA4B;oBACxB,0CAA0C,EAC9C,iBAAiB,CACpB,CAAC;AACT,SAAA;AACJ,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACR,QAAA,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAW,CAAC,CAAC;AAC5D,KAAA;AACD,IAAA,OAAO,oBAAoB,CAAC;AAChC,CAAC;AAED;;;;AAIG;SACa,2BAA2B,GAAA;AACvC,IAAA,IAAI,cAAmC,CAAC;IACxC,IAAI;AACA,QAAA,cAAc,GAAG,MAAM,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;;QAE7D,OAAO,cAAc,EAAE,OAAO,CAAC,yBAAyB,CAAC,KAAK,MAAM,CAAC;AACxE,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACR,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;AACL,CAAC;AAED;;;;;;AAMG;AACG,SAAU,qBAAqB,CACjC,MAA4B,EAC5B,MAAc,EACd,oBAA2C,EAC3C,oBAA2C,EAAA;AAE3C,IAAA,MAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;AAC7C,IAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE;AACpC,QAAA,MAAM,CAAC,KAAK,CACR,4EAA4E,CAC/E,CAAC;;AAEF,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;IAED,IAAI,CAAC,oBAAoB,EAAE;AACvB,QAAA,MAAM,CAAC,KAAK,CACR,mFAAmF,CACtF,CAAC;;AAEF,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;AAED,IAAA,IAAI,oBAAoB,EAAE;AACtB,QAAA,QAAQ,oBAAoB;YACxB,KAAK,oBAAoB,CAAC,MAAM,CAAC;YACjC,KAAK,oBAAoB,CAAC,GAAG;AACzB,gBAAA,MAAM,CAAC,KAAK,CACR,0EAA0E,CAC7E,CAAC;AACF,gBAAA,OAAO,IAAI,CAAC;AAChB,YAAA;AACI,gBAAA,MAAM,CAAC,KAAK,CACR,+EAA+E,CAClF,CAAC;AACF,gBAAA,OAAO,KAAK,CAAC;AACpB,SAAA;AACJ,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AAChB;;;;"}