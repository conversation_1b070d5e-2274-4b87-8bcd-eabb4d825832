{"version": 3, "file": "SignedHttpRequest.mjs", "sources": ["../../src/crypto/SignedHttpRequest.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;MAiBU,iBAAiB,CAAA;IAM1B,WACI,CAAA,aAA0C,EAC1C,UAAqC,EAAA;QAErC,MAAM,aAAa,GAAG,CAAC,UAAU,IAAI,UAAU,CAAC,aAAa,KAAK,EAAE,CAAC;AACrE,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/D,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;KACtC;AAED;;;AAGG;AACH,IAAA,MAAM,2BAA2B,GAAA;AAC7B,QAAA,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACpD,IAAI,CAAC,aAAa,CACrB,CAAC;AAEF,QAAA,OAAO,GAAG,CAAC;KACd;AAED;;;;;;AAMG;AACH,IAAA,MAAM,WAAW,CACb,OAAe,EACf,mBAA2B,EAC3B,MAAe,EAAA;AAEf,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,CACrC,OAAO,EACP,mBAAmB,EACnB,IAAI,CAAC,aAAa,EAClB,MAAM,CACT,CAAC;KACL;AAED;;;;AAIG;IACH,MAAM,UAAU,CAAC,mBAA2B,EAAA;QACxC,OAAO,IAAI,CAAC,SAAS;aAChB,qBAAqB,CAAC,mBAAmB,CAAC;AAC1C,aAAA,IAAI,CAAC,MAAM,IAAI,CAAC;AAChB,aAAA,KAAK,CAAC,CAAC,KAAK,KAAI;AACb;;AAEG;YACH,IACI,KAAK,YAAY,eAAe;AAChC,gBAAA,KAAK,CAAC,SAAS;oBACX,oBAAoB,CAAC,oBAAoB,EAC/C;AACE,gBAAA,OAAO,KAAK,CAAC;AAChB,aAAA;AAED,YAAA,MAAM,KAAK,CAAC;AAChB,SAAC,CAAC,CAAC;KACV;AACJ;;;;"}