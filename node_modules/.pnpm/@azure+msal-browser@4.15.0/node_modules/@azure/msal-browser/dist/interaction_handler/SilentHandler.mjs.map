{"version": 3, "file": "SilentHandler.mjs", "sources": ["../../src/interaction_handler/SilentHandler.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.emptyNavigateUri", "BrowserAuthErrorCodes.monitorWindowTimeout"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAsBH;;;;AAIG;AACI,eAAe,mBAAmB,CACrC,UAAkB,EAClB,iBAAqC,EACrC,MAAc,EACd,aAAqB,EACrB,iBAA0B,EAAA;IAE1B,iBAAiB,CAAC,mBAAmB,CACjC,iBAAiB,CAAC,gCAAgC,EAClD,aAAa,CAChB,CAAC;IAEF,IAAI,CAAC,UAAU,EAAE;;AAEb,QAAA,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;AACrC,QAAA,MAAM,sBAAsB,CAACA,gBAAsC,CAAC,CAAC;AACxE,KAAA;AACD,IAAA,IAAI,iBAAiB,EAAE;QACnB,OAAO,WAAW,CACd,SAAS,EACT,iBAAiB,CAAC,sBAAsB,EACxC,MAAM,EACN,iBAAiB,EACjB,aAAa,CAChB,CAAC,UAAU,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;AACtE,KAAA;AACD,IAAA,OAAO,MAAM,CACT,aAAa,EACb,iBAAiB,CAAC,0BAA0B,EAC5C,MAAM,EACN,iBAAiB,EACjB,aAAa,CAChB,CAAC,UAAU,CAAC,CAAC;AAClB,CAAC;AAEM,eAAe,kBAAkB,CACpC,MAA4B,EAC5B,SAAoB,EACpB,OAAsC,EACtC,MAAc,EACd,iBAAqC,EAAA;AAErC,IAAA,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;AACnC,IAAA,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;AACxB,QAAA,MAAM,qCAAqC,CAAC;AAC/C,KAAA;AACD,IAAA,MAAM,IAAI,GAAG,MAAM,UAAU,CACzB,KAAK,CAAC,eAAe,EACrB,MAAM,EACN,SAAS,EACT,OAAO,EACP,MAAM,EACN,iBAAiB,CACpB,CAAC;IACF,IAAI,CAAC,MAAM,EAAE,CAAC;AACd,IAAA,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;AAIG;AACI,eAAe,oBAAoB,CACtC,MAAyB,EACzB,OAAe,EACf,wBAAgC,EAChC,iBAAqC,EACrC,MAAc,EACd,aAAqB,EACrB,YAAgC,EAAA;IAEhC,iBAAiB,CAAC,mBAAmB,CACjC,iBAAiB,CAAC,iCAAiC,EACnD,aAAa,CAChB,CAAC;IAEF,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,KAAI;QAC3C,IAAI,OAAO,GAAG,yBAAyB,EAAE;YACrC,MAAM,CAAC,OAAO,CACV,CAAA,kEAAA,EAAqE,OAAO,CAAyB,sBAAA,EAAA,yBAAyB,CAAmC,iCAAA,CAAA,CACpK,CAAC;AACL,SAAA;AAED;;;AAGG;AACH,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAK;AACrC,YAAA,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YACjC,MAAM,CACF,sBAAsB,CAClBC,oBAA0C,CAC7C,CACJ,CAAC;SACL,EAAE,OAAO,CAAC,CAAC;AAEZ,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,MAAK;YACvC,IAAI,IAAI,GAAW,EAAE,CAAC;AACtB,YAAA,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;YAC3C,IAAI;AACA;;;;AAIG;AACH,gBAAA,IAAI,GAAG,aAAa,GAAG,aAAa,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC;AAC3D,aAAA;YAAC,OAAO,CAAC,EAAE,GAAE;AAEd,YAAA,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,aAAa,EAAE;gBACjC,OAAO;AACV,aAAA;YAED,IAAI,cAAc,GAAG,EAAE,CAAC;AACxB,YAAA,IAAI,aAAa,EAAE;AACf,gBAAA,IAAI,YAAY,KAAK,kBAAkB,CAAC,KAAK,EAAE;AAC3C,oBAAA,cAAc,GAAG,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC;AAClD,iBAAA;AAAM,qBAAA;AACH,oBAAA,cAAc,GAAG,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;AAChD,iBAAA;AACJ,aAAA;AACD,YAAA,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;AAC/B,YAAA,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YACjC,OAAO,CAAC,cAAc,CAAC,CAAC;SAC3B,EAAE,wBAAwB,CAAC,CAAC;AACjC,KAAC,CAAC,CAAC,OAAO,CAAC,MAAK;AACZ,QAAA,MAAM,CACF,kBAAkB,EAClB,iBAAiB,CAAC,kBAAkB,EACpC,MAAM,EACN,iBAAiB,EACjB,aAAa,CAChB,CAAC,MAAM,CAAC,CAAC;AACd,KAAC,CAAC,CAAC;AACP,CAAC;AAED;;;;;AAKG;AACH,SAAS,SAAS,CACd,WAAmB,EACnB,iBAAyB,EACzB,iBAAqC,EACrC,aAAqB,EAAA;IAErB,iBAAiB,CAAC,mBAAmB,CACjC,iBAAiB,CAAC,sBAAsB,EACxC,aAAa,CAChB,CAAC;AAEF;;;AAGG;IAEH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACnC,QAAA,MAAM,WAAW,GAAG,kBAAkB,EAAE,CAAC;AAEzC,QAAA,MAAM,CAAC,UAAU,CAAC,MAAK;YACnB,IAAI,CAAC,WAAW,EAAE;gBACd,MAAM,CAAC,uBAAuB,CAAC,CAAC;gBAChC,OAAO;AACV,aAAA;AAED,YAAA,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC;YAE9B,OAAO,CAAC,WAAW,CAAC,CAAC;SACxB,EAAE,iBAAiB,CAAC,CAAC;AAC1B,KAAC,CAAC,CAAC;AACP,CAAC;AACD;;;;;;AAMG;AACH,SAAS,aAAa,CAAC,WAAmB,EAAA;AACtC,IAAA,MAAM,WAAW,GAAG,kBAAkB,EAAE,CAAC;AAEzC,IAAA,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC;AAE9B,IAAA,OAAO,WAAW,CAAC;AACvB,CAAC;AAED;;;;AAIG;AACH,SAAS,kBAAkB,GAAA;IACvB,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAEnD,IAAA,SAAS,CAAC,SAAS,GAAG,kBAAkB,CAAC;AACzC,IAAA,SAAS,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AACtC,IAAA,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACtC,IAAA,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;AACrD,IAAA,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;AAC7B,IAAA,SAAS,CAAC,YAAY,CAClB,SAAS,EACT,6CAA6C,CAChD,CAAC;AACF,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAErC,IAAA,OAAO,SAAS,CAAC;AACrB,CAAC;AAED;;;;AAIG;AACH,SAAS,kBAAkB,CAAC,MAAyB,EAAA;AACjD,IAAA,IAAI,QAAQ,CAAC,IAAI,KAAK,MAAM,CAAC,UAAU,EAAE;AACrC,QAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACrC,KAAA;AACL;;;;"}