{"version": 3, "file": "SignInApiClient.mjs", "sources": ["../../../../../../../src/custom_auth/core/network_client/custom_auth_api/SignInApiClient.ts"], "sourcesContent": [null], "names": ["CustomAuthApiEndpoint.SIGNIN_INITIATE", "CustomAuthApiEndpoint.SIGNIN_CHALLENGE", "CustomAuthApiEndpoint.SIGNIN_TOKEN", "CustomAuthApiErrorCode.ACCESS_TOKEN_MISSING", "CustomAuthApiErrorCode.ID_TOKEN_MISSING", "CustomAuthApiErrorCode.REFRESH_TOKEN_MISSING", "CustomAuthApiErrorCode.INVALID_EXPIRES_IN", "CustomAuthApiErrorCode.INVALID_TOKEN_TYPE", "CustomAuthApiErrorCode.CLIENT_INFO_MISSING"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAqBG,MAAO,eAAgB,SAAQ,aAAa,CAAA;AAC9C;;;;AAIG;IACH,MAAM,QAAQ,CACV,MAA6B,EAAA;QAE7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC7BA,eAAqC,EACrC;YACI,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,cAAc,EAAE,MAAM,CAAC,cAAc;SACxC,EACD,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,aAAa,CACvB,CAAC;QAEF,IAAI,CAAC,8BAA8B,CAC/B,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,aAAa,CACvB,CAAC;AAEF,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;;AAIG;IACH,MAAM,gBAAgB,CAClB,MAA8B,EAAA;QAE9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC7BC,gBAAsC,EACtC;YACI,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;YAC7C,cAAc,EAAE,MAAM,CAAC,cAAc;SACxC,EACD,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,aAAa,CACvB,CAAC;QAEF,IAAI,CAAC,8BAA8B,CAC/B,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,aAAa,CACvB,CAAC;AAEF,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;;;AAKG;IACH,MAAM,yBAAyB,CAC3B,MAAkC,EAAA;QAElC,OAAO,IAAI,CAAC,aAAa,CACrB;YACI,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;YAC7C,UAAU,EAAE,SAAS,CAAC,QAAQ;YAC9B,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC5B,EACD,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,aAAa,CACvB,CAAC;KACL;IAED,MAAM,oBAAoB,CACtB,MAA6B,EAAA;QAE7B,OAAO,IAAI,CAAC,aAAa,CACrB;YACI,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;YAC7C,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,GAAG,EAAE,MAAM,CAAC,GAAG;YACf,UAAU,EAAE,SAAS,CAAC,GAAG;SAC5B,EACD,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,aAAa,CACvB,CAAC;KACL;IAED,MAAM,iCAAiC,CACnC,MAAsC,EAAA;QAEtC,OAAO,IAAI,CAAC,aAAa,CACrB;YACI,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;YAC7C,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,UAAU,EAAE,SAAS,CAAC,kBAAkB;AACxC,YAAA,WAAW,EAAE,IAAI;SACpB,EACD,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,aAAa,CACvB,CAAC;KACL;AAEO,IAAA,MAAM,aAAa,CACvB,WAA6C,EAC7C,gBAAwC,EACxC,aAAqB,EAAA;;AAGrB,QAAA,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;AAE/B,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC7BC,YAAkC,EAClC,WAAW,EACX,gBAAgB,EAChB,aAAa,CAChB,CAAC;AAEF,QAAA,eAAe,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;AAEnD,QAAA,OAAO,MAAM,CAAC;KACjB;IAEO,OAAO,0BAA0B,CACrC,aAAkC,EAAA;QAElC,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,gBAAgB,GAAG,EAAE,CAAC;AAE1B,QAAA,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;AAC7B,YAAA,SAAS,GAAGC,oBAA2C,CAAC;YACxD,gBAAgB,GAAG,8CAA8C,CAAC;AACrE,SAAA;AAAM,aAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;AAChC,YAAA,SAAS,GAAGC,gBAAuC,CAAC;YACpD,gBAAgB,GAAG,0CAA0C,CAAC;AACjE,SAAA;AAAM,aAAA,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;AACrC,YAAA,SAAS,GAAGC,qBAA4C,CAAC;YACzD,gBAAgB,GAAG,+CAA+C,CAAC;AACtE,SAAA;aAAM,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,aAAa,CAAC,UAAU,IAAI,CAAC,EAAE;AACnE,YAAA,SAAS,GAAGC,kBAAyC,CAAC;YACtD,gBAAgB,GAAG,4CAA4C,CAAC;AACnE,SAAA;AAAM,aAAA,IAAI,aAAa,CAAC,UAAU,KAAK,QAAQ,EAAE;AAC9C,YAAA,SAAS,GAAGC,kBAAyC,CAAC;AACtD,YAAA,gBAAgB,GAAG,CAAe,YAAA,EAAA,aAAa,CAAC,UAAU,mCAAmC,CAAC;AACjG,SAAA;AAAM,aAAA,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;AACnC,YAAA,SAAS,GAAGC,mBAA0C,CAAC;YACvD,gBAAgB,GAAG,6CAA6C,CAAC;AACpE,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,IAAI,CAAC,gBAAgB,EAAE;YACjC,OAAO;AACV,SAAA;QAED,MAAM,IAAI,kBAAkB,CACxB,SAAS,EACT,gBAAgB,EAChB,aAAa,CAAC,cAAc,CAC/B,CAAC;KACL;AACJ;;;;"}