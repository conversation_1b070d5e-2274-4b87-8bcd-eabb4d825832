/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { AuthFlowStateBase } from '../../../core/auth_flow/AuthFlowState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * State of a reset password operation that has failed.
 */
class ResetPasswordFailedState extends AuthFlowStateBase {
}

export { ResetPasswordFailedState };
//# sourceMappingURL=ResetPasswordFailedState.mjs.map
