{"version": 3, "file": "CustomAuthAuthority.mjs", "sources": ["../../../../../src/custom_auth/core/CustomAuthAuthority.ts"], "sourcesContent": [null], "names": ["CustomAuthApiEndpoint.SIGNIN_TOKEN"], "mappings": ";;;;;;AAAA;;;AAGG;AAaH;;AAEG;AACG,MAAO,mBAAoB,SAAQ,SAAS,CAAA;AAC9C;;;;;;;;AAQG;IACH,WACI,CAAA,SAAiB,EACjB,MAA4B,EAC5B,gBAAgC,EAChC,YAAiC,EACjC,MAAc,EACN,qBAA8B,EAAA;QAEtC,MAAM,gBAAgB,GAClB,mBAAmB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;AAE1D,QAAA,MAAM,gBAAgB,GAAqB;AACvC,YAAA,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY;AACtC,YAAA,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW;AACpC,YAAA,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB;AAC9C,YAAA,sBAAsB,EAAE,MAAM,CAAC,IAAI,CAAC,sBAAsB;AAC1D,YAAA,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,iBAAiB;AAChD,YAAA,0BAA0B,EAAE,MAAM,CAAC,IAAI,CAAC,0BAA0B;SACrE,CAAC;AAEF,QAAA,KAAK,CACD,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACZ,gBAAgB,EAChB,MAAM,EACN,EAAE,CACL,CAAC;QArBM,IAAqB,CAAA,qBAAA,GAArB,qBAAqB,CAAS;;AAwBtC,QAAA,MAAM,cAAc,GAAG;AACnB,YAAA,OAAO,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC;AAC/B,YAAA,eAAe,EAAE,IAAI,CAAC,iBAAiB,EAAE;YACzC,iBAAiB,EAAE,IAAI,CAAC,eAAe;YACvC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB;AAC5C,YAAA,sBAAsB,EAAE,EAAE;YAC1B,cAAc,EAAE,IAAI,CAAC,aAAa;AAClC,YAAA,oBAAoB,EAAE,EAAE;AACxB,YAAA,MAAM,EAAE,EAAE;AACV,YAAA,kBAAkB,EAAE,KAAK;AACzB,YAAA,oBAAoB,EAAE,KAAK;AAC3B;;;AAGG;YACH,SAAS,EAAE,MAAM,CAAC,gBAAgB;AAClC,YAAA,QAAQ,EAAE,EAAE;SACf,CAAC;AACF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,iCAAiC,CAChE,cAAc,CAAC,eAAe,CACjC,CAAC;AACF,QAAA,YAAY,CAAC,oBAAoB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;KAC/D;AAED;;;;;AAKG;IACH,sBAAsB,GAAA;AAClB;;;AAGG;QACH,OAAO,CAAC,IAAI,CAAC,qBAAqB;cAC5B,IAAI,CAAC,kBAAkB;AACzB,cAAE,IAAI,CAAC,qBAAqB,CAAC;KACpC;IAEQ,iBAAiB,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC;KAC/D;AAED,IAAA,IAAa,aAAa,GAAA;AACtB,QAAA,MAAM,WAAW,GAAG,QAAQ,CACxB,IAAI,CAAC,sBAAsB,EAAE,EAC7BA,YAAkC,CACrC,CAAC;QAEF,OAAO,WAAW,CAAC,IAAI,CAAC;KAC3B;AACJ;;;;"}