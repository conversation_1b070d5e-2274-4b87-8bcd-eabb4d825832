{"version": 3, "file": "IPublicClientApplication.d.ts", "sourceRoot": "", "sources": ["../../../src/app/IPublicClientApplication.ts"], "names": [], "mappings": "AAKA,OAAO,EACH,aAAa,EACb,WAAW,EACX,MAAM,EACN,2BAA2B,EAC9B,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC1D,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAClE,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AAKpE,OAAO,EAAE,UAAU,EAAE,MAAM,8BAA8B,CAAC;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,oCAAoC,CAAC;AACvE,OAAO,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAC9E,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAE,wBAAwB,EAAE,MAAM,wCAAwC,CAAC;AAClF,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAC;AAClE,OAAO,EAAE,oBAAoB,EAAE,MAAM,qCAAqC,CAAC;AAC3E,OAAO,EAAE,qBAAqB,EAAE,MAAM,0BAA0B,CAAC;AACjE,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACpE,OAAO,EAAE,4BAA4B,EAAE,MAAM,4CAA4C,CAAC;AAC1F,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAElD,MAAM,WAAW,wBAAwB;IAErC,UAAU,CAAC,OAAO,CAAC,EAAE,4BAA4B,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAClE,iBAAiB,CAAC,OAAO,EAAE,YAAY,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACxE,oBAAoB,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9D,kBAAkB,CACd,aAAa,EAAE,aAAa,GAC7B,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACjC,kBAAkB,CACd,OAAO,EAAE,wBAAwB,GAClC,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACjC,gBAAgB,CACZ,QAAQ,EAAE,qBAAqB,EAC/B,UAAU,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,GAC9B,MAAM,GAAG,IAAI,CAAC;IACjB,mBAAmB,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9C,sBAAsB,CAAC,QAAQ,EAAE,2BAA2B,GAAG,MAAM,CAAC;IACtE,yBAAyB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC;IACvD,0BAA0B,IAAI,IAAI,CAAC;IACnC,2BAA2B,IAAI,IAAI,CAAC;IACpC,UAAU,CAAC,aAAa,EAAE,aAAa,GAAG,WAAW,GAAG,IAAI,CAAC;IAC7D,kBAAkB,CAAC,aAAa,EAAE,MAAM,GAAG,WAAW,GAAG,IAAI,CAAC;IAC9D,mBAAmB,CAAC,OAAO,EAAE,MAAM,GAAG,WAAW,GAAG,IAAI,CAAC;IACzD,oBAAoB,CAAC,QAAQ,EAAE,MAAM,GAAG,WAAW,GAAG,IAAI,CAAC;IAC3D,cAAc,IAAI,WAAW,EAAE,CAAC;IAChC,qBAAqB,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAC3E,UAAU,CAAC,OAAO,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAClE,aAAa,CAAC,OAAO,CAAC,EAAE,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,aAAa,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACzD,cAAc,CAAC,aAAa,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACjE,WAAW,CAAC,aAAa,CAAC,EAAE,sBAAsB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACnE,SAAS,CAAC,OAAO,EAAE,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACpE,aAAa,IAAI,WAAW,CAAC;IAC7B,SAAS,IAAI,MAAM,CAAC;IACpB,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,gBAAgB,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI,GAAG,IAAI,CAAC;IACpD,gBAAgB,IAAI,WAAW,GAAG,IAAI,CAAC;IACvC,wBAAwB,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC;IACjE,mBAAmB,CAAC,gBAAgB,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAC/D,gBAAgB;IAChB,gBAAgB,IAAI,oBAAoB,CAAC;IACzC,YAAY,CACR,MAAM,EAAE,oBAAoB,EAC5B,OAAO,EACD,aAAa,GACb,gBAAgB,GAChB,eAAe,GACf,YAAY,GACnB,OAAO,CAAC,IAAI,CAAC,CAAC;IACjB,UAAU,CAAC,aAAa,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CAChE;AAED,eAAO,MAAM,8BAA8B,EAAE,wBAkK5C,CAAC"}