{"version": 3, "file": "BridgeProxy.d.ts", "sourceRoot": "", "sources": ["../../../src/naa/BridgeProxy.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,UAAU,EAAsB,MAAM,iBAAiB,CAAC;AACjE,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAC7C,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAC7D,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAE3D,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAOnD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAKjD,OAAO,CAAC,MAAM,CAAC;IACX,UAAU,MAAM;QACZ,mBAAmB,EAAE,UAAU,CAAC;KACnC;CACJ;AAED;;;;GAIG;AACH,qBAAa,WAAY,YAAW,YAAY;IAC5C,MAAM,CAAC,cAAc,EAAE,aAAa,EAAE,CAAM;IAC5C,OAAO,EAAE,MAAM,CAAC;IAChB,UAAU,EAAE,MAAM,CAAC;IACnB,YAAY,CAAC,EAAE,kBAAkB,CAAC;IAClC,cAAc,CAAC,EAAE,cAAc,CAAC;IAEhC;;;;;OAKG;qBACoB,6BAA6B,IAAI,OAAO,CAAC,WAAW,CAAC;IA4D5E;;;;OAIG;IACI,mBAAmB,CAAC,OAAO,EAAE,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;IAItE;;;;OAIG;IACI,cAAc,CAAC,OAAO,EAAE,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC;YAInD,QAAQ;IAaf,mBAAmB,IAAI,kBAAkB,GAAG,IAAI;IAIhD,iBAAiB,IAAI,cAAc,GAAG,IAAI;IAIjD,OAAO,CAAC,MAAM,CAAC,YAAY;IAe3B;;;;OAIG;IACH,OAAO,CAAC,WAAW;IAsBnB,OAAO,CAAC,MAAM,CAAC,2BAA2B;IAU1C;;;;;OAKG;IACH,OAAO;IAYP;;;OAGG;WACiB,MAAM,IAAI,OAAO,CAAC,YAAY,CAAC;CAStD;AAED,eAAe,WAAW,CAAC"}