/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { AuthFlowStateBase } from '../../../core/auth_flow/AuthFlowState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * The completed state of the sign-out flow.
 */
class SignOutCompletedState extends AuthFlowStateBase {
}
/**
 * The failed state of the sign-out flow.
 */
class SignOutFailedState extends AuthFlowStateBase {
}

export { SignOutCompletedState, SignOutFailedState };
//# sourceMappingURL=SignOutState.mjs.map
