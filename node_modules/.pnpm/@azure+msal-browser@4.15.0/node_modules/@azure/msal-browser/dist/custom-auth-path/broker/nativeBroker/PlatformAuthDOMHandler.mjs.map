{"version": 3, "file": "PlatformAuthDOMHandler.mjs", "sources": ["../../../../../src/broker/nativeBroker/PlatformAuthDOMHandler.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;MAsBU,sBAAsB,CAAA;AAM/B,IAAA,WAAA,CACI,MAAc,EACd,iBAAqC,EACrC,aAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACnC,QAAA,IAAI,CAAC,gBAAgB,GAAG,qBAAqB,CAAC,qBAAqB,CAAC;KACvE;IAED,aAAa,cAAc,CACvB,MAAc,EACd,iBAAqC,EACrC,aAAqB,EAAA;AAErB,QAAA,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;;AAG9D,QAAA,IAAI,MAAM,CAAC,SAAS,EAAE,sBAAsB,EAAE;AAC1C,YAAA,MAAM,kBAAkB;;AAEpB,YAAA,MAAM,MAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,qBAAqB,CAC/D,qBAAqB,CAAC,wBAAwB,CACjD,CAAC;YACN,IACI,kBAAkB,EAAE,QAAQ,CACxB,qBAAqB,CAAC,iBAAiB,CAC1C,EACH;AACE,gBAAA,MAAM,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;gBACnD,OAAO,IAAI,sBAAsB,CAC7B,MAAM,EACN,iBAAiB,EACjB,aAAa,CAChB,CAAC;AACL,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,SAAS,CAAC;KACpB;AAED;;;AAGG;IACH,cAAc,GAAA;QACV,OAAO,qBAAqB,CAAC,wBAAwB,CAAC;KACzD;IAED,mBAAmB,GAAA;AACf,QAAA,OAAO,EAAE,CAAC;KACb;IAED,gBAAgB,GAAA;QACZ,OAAO,qBAAqB,CAAC,YAAY,CAAC;KAC7C;AAED;;;;AAIG;IACH,MAAM,WAAW,CACb,OAA4B,EAAA;QAE5B,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB,GAAG,uCAAuC,CAClE,CAAC;QAEF,IAAI;YACA,MAAM,kBAAkB,GACpB,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;AAC/C,YAAA,MAAM,QAAQ;;YAEV,MAAM,MAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,eAAe,CACzD,kBAAkB,CACrB,CAAC;AACN,YAAA,OAAO,IAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,CAAC;AACxD,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB,GAAG,kCAAkC,CAC7D,CAAC;AACF,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAEO,IAAA,4BAA4B,CAChC,OAA4B,EAAA;QAE5B,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB,GAAG,sCAAsC,CACjE,CAAC;QAEF,MAAM,EACF,SAAS,EACT,QAAQ,EACR,SAAS,EACT,KAAK,EACL,WAAW,EACX,aAAa,EACb,KAAK,EACL,YAAY,EACZ,gBAAgB,EAChB,eAAe,EACf,GAAG,mBAAmB,EACzB,GAAG,OAAO,CAAC;QAEZ,MAAM,oBAAoB,GACtB,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;AAEhD,QAAA,MAAM,kBAAkB,GAA4B;AAChD,YAAA,SAAS,EAAE,SAAS;AACpB,YAAA,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE;AAC/B,YAAA,SAAS,EAAE,SAAS;AACpB,YAAA,QAAQ,EAAE,QAAQ;AAClB,YAAA,aAAa,EAAE,aAAa,IAAI,IAAI,CAAC,aAAa;AAClD,YAAA,eAAe,EAAE,EAAE,GAAG,eAAe,EAAE,GAAG,oBAAoB,EAAE;AAChE,YAAA,sBAAsB,EAAE,KAAK;AAC7B,YAAA,WAAW,EAAE,WAAW;AACxB,YAAA,KAAK,EAAE,KAAK;AACZ,YAAA,KAAK,EAAE,KAAK;AACZ,YAAA,YAAY,EAAE,YAAY;AAC1B,YAAA,gBAAgB,EAAE,gBAAgB;SACrC,CAAC;AAEF,QAAA,OAAO,kBAAkB,CAAC;KAC7B;AAEO,IAAA,8BAA8B,CAClC,QAAgB,EAAA;AAEhB,QAAA,IAAI,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;AACtC,YAAA,IACI,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC;AACtC,gBAAA,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;AAClC,gBAAA,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC;AACrC,gBAAA,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;AAClC,gBAAA,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC;AACjC,gBAAA,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,EACtC;AACE,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB;AACjB,oBAAA,2DAA2D,CAClE,CAAC;AACF,gBAAA,OAAO,IAAI,CAAC,+BAA+B,CACvC,QAAoC,CACvC,CAAC;AACL,aAAA;AAAM,iBAAA,IAAI,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;gBACzC,MAAM,aAAa,GAAG,QAAoC,CAAC;AAC3D,gBAAA,IACI,aAAa,CAAC,SAAS,KAAK,KAAK;AACjC,oBAAA,aAAa,CAAC,KAAK;AACnB,oBAAA,aAAa,CAAC,KAAK,CAAC,IAAI,EAC1B;AACE,oBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB;AACjB,wBAAA,4CAA4C,CACnD,CAAC;AACF,oBAAA,MAAM,qBAAqB,CACvB,aAAa,CAAC,KAAK,CAAC,IAAI,EACxB,aAAa,CAAC,KAAK,CAAC,WAAW,EAC/B;wBACI,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC;AAC9C,wBAAA,cAAc,EAAE,aAAa,CAAC,KAAK,CAAC,aAAa;AACjD,wBAAA,MAAM,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM;AAClC,wBAAA,UAAU,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU;AAC7C,qBAAA,CACJ,CAAC;AACL,iBAAA;AACJ,aAAA;AACJ,SAAA;QACD,MAAM,eAAe,CACjB,cAAc,CAAC,eAAe,EAC9B,uCAAuC,CAC1C,CAAC;KACL;AAEO,IAAA,+BAA+B,CACnC,QAAkC,EAAA;QAElC,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB,GAAG,mCAAmC,CAC9D,CAAC;AACF,QAAA,MAAM,cAAc,GAAyB;YACzC,YAAY,EAAE,QAAQ,CAAC,WAAW;YAClC,QAAQ,EAAE,QAAQ,CAAC,OAAO;YAC1B,WAAW,EAAE,QAAQ,CAAC,UAAU;YAChC,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,UAAU,EAAE,QAAQ,CAAC,SAAS;YAC9B,KAAK,EAAE,QAAQ,CAAC,MAAM;AACtB,YAAA,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,EAAE;AAC3B,YAAA,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,EAAE;AACrC,YAAA,qBAAqB,EAAE,QAAQ,CAAC,qBAAqB,IAAI,KAAK;YAC9D,GAAG,EAAE,QAAQ,CAAC,wBAAwB;SACzC,CAAC;AAEF,QAAA,OAAO,cAAc,CAAC;KACzB;AAEO,IAAA,iBAAiB,CACrB,eAAwC,EAAA;QAExC,MAAM,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,MAAM,CAC5D,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,KAAI;YACrB,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5B,YAAA,OAAO,MAAM,CAAC;SACjB,EACD,EAAgB,CACnB,CAAC;AAEF,QAAA,MAAM,gBAAgB,GAAuB;AACzC,YAAA,GAAG,iBAAiB;SACvB,CAAC;AAEF,QAAA,OAAO,gBAAgB,CAAC;KAC3B;AACJ;;;;"}