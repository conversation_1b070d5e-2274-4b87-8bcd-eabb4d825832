{"version": 3, "file": "SignUpError.mjs", "sources": ["../../../../../../../src/custom_auth/sign_up/auth_flow/error_type/SignUpError.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAIG,MAAO,WAAY,SAAQ,mBAAmB,CAAA;AAChD;;;AAGG;IACH,mBAAmB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,wBAAwB,EAAE,CAAC;KAC1C;AAED;;;AAGG;IACH,iBAAiB,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;KACpC;AAED;;;AAGG;IACH,iBAAiB,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAC;KAC3C;AAED;;;AAGG;IACH,2BAA2B,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,wBAAwB,EAAE,CAAC;KAC1C;AAED;;;AAGG;IACH,4BAA4B,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,gCAAgC,EAAE,CAAC;KAClD;AAED;;;AAGG;IACH,0BAA0B,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,+BAA+B,EAAE,CAAC;KACjD;AAED;;;AAGG;IACH,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;KACjC;AACJ,CAAA;AAEK,MAAO,yBAA0B,SAAQ,mBAAmB,CAAA;AAC9D;;;AAGG;IACH,iBAAiB,GAAA;QACb,QACI,IAAI,CAAC,wBAAwB,EAAE,IAAI,IAAI,CAAC,yBAAyB,EAAE,EACrE;KACL;AAED;;;AAGG;IACH,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;KACjC;AACJ,CAAA;AAEK,MAAO,qBAAsB,SAAQ,mBAAmB,CAAA;AAC1D;;;AAGG;IACH,aAAa,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;KACpC;AAED;;;AAGG;IACH,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;KACjC;AACJ,CAAA;AAEK,MAAO,2BAA4B,SAAQ,mBAAmB,CAAA;AAChE;;;AAGG;IACH,2BAA2B,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,wBAAwB,EAAE,CAAC;KAC1C;AAED;;;AAGG;IACH,4BAA4B,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,gCAAgC,EAAE,CAAC;KAClD;AAED;;;AAGG;IACH,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;KACjC;AACJ,CAAA;AAEK,MAAO,qBAAsB,SAAQ,mBAAmB,CAAA;AAC1D;;;AAGG;IACH,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;KACjC;AACJ;;;;"}