{"version": 3, "file": "AuthFlowErrorBase.mjs", "sources": ["../../../../../../src/custom_auth/core/auth_flow/AuthFlowErrorBase.ts"], "sourcesContent": [null], "names": ["CustomAuthApiErrorCode.USER_NOT_FOUND", "CustomAuthApiErrorCode.INVALID_REQUEST", "CustomAuthApiErrorCode.UNSUPPORTED_CHALLENGE_TYPE", "CustomAuthApiErrorCode.INVALID_GRANT", "CustomAuthApiSuberror.INVALID_OOB_VALUE", "CustomAuthApiSuberror.PASSWORD_BANNED", "CustomAuthApiSuberror.PASSWORD_IS_INVALID", "CustomAuthApiSuberror.PASSWORD_RECENTLY_USED", "CustomAuthApiSuberror.PASSWORD_TOO_LONG", "CustomAuthApiSuberror.PASSWORD_TOO_SHORT", "CustomAuthApiSuberror.PASSWORD_TOO_WEAK", "CustomAuthApiErrorCode.USER_ALREADY_EXISTS", "CustomAuthApiErrorCode.ATTRIBUTES_REQUIRED", "CustomAuthApiSuberror.ATTRIBUTE_VALIATION_FAILED", "CustomAuthApiErrorCode.EXPIRED_TOKEN"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAWH;;AAEG;MACmB,iBAAiB,CAAA;AACnC,IAAA,WAAA,CAAmB,SAA0B,EAAA;QAA1B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAiB;KAAI;IAEvC,mBAAmB,GAAA;QACzB,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,KAAKA,cAAqC,CAAC;KACzE;IAES,kBAAkB,GAAA;AACxB,QAAA,QACI,CAAC,IAAI,CAAC,SAAS,YAAY,oBAAoB;YAC3C,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,UAAU,CAAC;AACzD,aAAC,IAAI,CAAC,SAAS,YAAY,kBAAkB;gBACzC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CACvC,0CAA0C,CAC7C;AACD,gBAAA,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,EACnD;KACL;IAES,+BAA+B,GAAA;QACrC,QACI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,KAAKC,eAAsC;aAC3D,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CACtC,0EAA0E,CAC7E;AACG,gBAAA,KAAK,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,KAAK;gBAChBC,0BAAiD,EACvD;KACL;IAES,wBAAwB,GAAA;QAC9B,MAAM,mBAAmB,GACrB,IAAI,CAAC,SAAS,CAAC,KAAK,KAAKC,aAAoC;YAC7D,IAAI,CAAC,SAAS,YAAY,kBAAkB;AAC5C,YAAA,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AAEtD,QAAA,MAAM,eAAe,GACjB,IAAI,CAAC,SAAS,YAAY,oBAAoB;YAC9C,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC;QAEnE,OAAO,mBAAmB,IAAI,eAAe,CAAC;KACjD;IAES,kBAAkB,GAAA;QACxB,QACI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,KAAKA,aAAoC;YAC1D,IAAI,CAAC,SAAS,YAAY,kBAAkB;YAC5C,IAAI,CAAC,SAAS,CAAC,QAAQ;gBACnBC,iBAAuC;AAC/C,aAAC,IAAI,CAAC,SAAS,YAAY,oBAAoB;AAC3C,gBAAA,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,EACjE;KACL;IAES,eAAe,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,SAAS,YAAY,aAAa,CAAC;KAClD;IAES,yBAAyB,GAAA;AAC/B,QAAA,MAAM,wBAAwB,GAAG,IAAI,GAAG,CAAS;AAC7C,YAAAC,eAAqC;AACrC,YAAAC,mBAAyC;AACzC,YAAAC,sBAA4C;AAC5C,YAAAC,iBAAuC;AACvC,YAAAC,kBAAwC;AACxC,YAAAC,iBAAuC;AAC1C,SAAA,CAAC,CAAC;AAEH,QAAA,QACI,IAAI,CAAC,SAAS,YAAY,kBAAkB;AAC5C,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,KAAKP,aAAoC;AAC7D,YAAA,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,EAAE,CAAC,EAC7D;KACL;IAES,wBAAwB,GAAA;AAC9B,QAAA,QACI,IAAI,CAAC,SAAS,YAAY,kBAAkB;YAC5C,IAAI,CAAC,SAAS,CAAC,KAAK,KAAKQ,mBAA0C,EACrE;KACL;IAES,wBAAwB,GAAA;AAC9B,QAAA,QACI,IAAI,CAAC,SAAS,YAAY,kBAAkB;YAC5C,IAAI,CAAC,SAAS,CAAC,KAAK,KAAKC,mBAA0C,EACrE;KACL;IAES,gCAAgC,GAAA;AACtC,QAAA,QACI,CAAC,IAAI,CAAC,SAAS,YAAY,kBAAkB;AACzC,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,KAAKT,aAAoC;YAC7D,IAAI,CAAC,SAAS,CAAC,QAAQ;gBACnBU,0BAAgD;AACxD,aAAC,IAAI,CAAC,SAAS,YAAY,oBAAoB;gBAC3C,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,YAAY,CAAC;oBACnD,IAAI,CAAC,EACf;KACL;IAES,2BAA2B,GAAA;AACjC,QAAA,OAAO,IAAI,CAAC,SAAS,YAAY,yBAAyB,CAAC;KAC9D;IAES,mBAAmB,GAAA;AACzB,QAAA,QACI,IAAI,CAAC,SAAS,YAAY,kBAAkB;YAC5C,IAAI,CAAC,SAAS,CAAC,KAAK,KAAKC,aAAoC,EAC/D;KACL;AACJ,CAAA;AAEK,MAAgB,mBAAoB,SAAQ,iBAAiB,CAAA;AAC/D;;;AAGG;IACH,cAAc,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;KACrC;AACJ;;;;"}