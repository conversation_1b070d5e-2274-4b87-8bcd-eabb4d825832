/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { AuthFlowResultBase } from '../../../core/auth_flow/AuthFlowResultBase.mjs';
import { ResetPasswordError } from '../error_type/ResetPasswordError.mjs';
import { ResetPasswordCodeRequiredState } from '../state/ResetPasswordCodeRequiredState.mjs';
import { ResetPasswordFailedState } from '../state/ResetPasswordFailedState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Result of a reset password operation.
 */
class ResetPasswordStartResult extends AuthFlowResultBase {
    /**
     * Creates a new instance of ResetPasswordStartResult.
     * @param state The state of the result.
     */
    constructor(state) {
        super(state);
    }
    /**
     * Creates a new instance of ResetPasswordStartResult with an error.
     * @param error The error that occurred.
     * @returns {ResetPasswordStartResult} A new instance of ResetPasswordStartResult with the error set.
     */
    static createWithError(error) {
        const result = new ResetPasswordStartResult(new ResetPasswordFailedState());
        result.error = new ResetPasswordError(ResetPasswordStartResult.createErrorData(error));
        return result;
    }
    /**
     * Checks if the result is in a failed state.
     */
    isFailed() {
        return this.state instanceof ResetPasswordFailedState;
    }
    /**
     * Checks if the result is in a code required state.
     */
    isCodeRequired() {
        return this.state instanceof ResetPasswordCodeRequiredState;
    }
}

export { ResetPasswordStartResult };
//# sourceMappingURL=ResetPasswordStartResult.mjs.map
