{"version": 3, "file": "BrowserCrypto.mjs", "sources": ["../../../../src/crypto/BrowserCrypto.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.nonBrowserEnvironment", "BrowserAuthErrorCodes.cryptoNonExistent", "BrowserAuthErrorCodes.failedToDecryptEarResponse"], "mappings": ";;;;;;;;;AAAA;;;AAGG;AAcH;;;AAGG;AAEH;;AAEG;AACH;AACA,MAAM,oBAAoB,GAAG,mBAAmB,CAAC;AACjD,MAAM,OAAO,GAAG,SAAS,CAAC;AAC1B,MAAM,IAAI,GAAG,MAAM,CAAC;AACpB;AACA,MAAM,aAAa,GAAG,SAAS,CAAC;AAChC;AACA,MAAM,cAAc,GAAG,IAAI,CAAC;AAC5B;AACA,MAAM,eAAe,GAAe,IAAI,UAAU,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACvE;AACA,MAAM,UAAU,GAAG,kBAAkB,CAAC;AACtC;AACA,MAAM,UAAU,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AAEtC;AACA,MAAM,GAAG,GAAG,KAAK,CAAC;AAClB;AACA,MAAM,OAAO,GAAG,SAAS,CAAC;AAC1B,MAAM,OAAO,GAAG,SAAS,CAAC;AAC1B,MAAM,UAAU,GAAG,WAAW,CAAC;AAE/B;AACA,MAAM,eAAe,GAAG,yBAAyB,CAAC;AAElD,MAAM,sBAAsB,GAA0B;AAClD,IAAA,IAAI,EAAE,oBAAoB;AAC1B,IAAA,IAAI,EAAE,aAAa;AACnB,IAAA,aAAa,EAAE,cAAc;AAC7B,IAAA,cAAc,EAAE,eAAe;CAClC,CAAC;AAEF;;AAEG;AACG,SAAU,uBAAuB,CACnC,wBAAiC,EAAA;IAEjC,IAAI,CAAC,MAAM,EAAE;AACT,QAAA,MAAM,sBAAsB,CACxBA,qBAA2C,CAC9C,CAAC;AACL,KAAA;AACD,IAAA,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AAChB,QAAA,MAAM,sBAAsB,CAACC,iBAAuC,CAAC,CAAC;AACzE,KAAA;IACD,IAAI,CAAC,wBAAwB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;QACpD,MAAM,sBAAsB,CACxBA,iBAAuC,EACvC,eAAe,CAClB,CAAC;AACL,KAAA;AACL,CAAC;AAED;;;;;AAKG;AACI,eAAe,YAAY,CAC9B,UAAkB,EAClB,iBAAsC,EACtC,aAAsB,EAAA;IAEtB,iBAAiB,EAAE,mBAAmB,CAClC,iBAAiB,CAAC,YAAY,EAC9B,aAAa,CAChB,CAAC;AACF,IAAA,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAClC,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACxC,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAC9B,aAAa,EACb,IAAI,CACiB,CAAC;AAC9B,CAAC;AAED;;;AAGG;AACG,SAAU,eAAe,CAAC,UAAsB,EAAA;IAClD,OAAO,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AACrD,CAAC;AAED;;;AAGG;AACH,SAAS,eAAe,GAAA;AACpB,IAAA,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AAC1C,IAAA,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AAED;;;;AAIG;SACa,aAAa,GAAA;AACzB,IAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACpC,IAAA,MAAM,QAAQ,GAAG,eAAe,EAAE,GAAG,KAAK,IAAI,eAAe,EAAE,GAAG,KAAK,CAAC,CAAC;;AAGzE,IAAA,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;;AAEjC,IAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;;IAE7C,MAAM,OAAO,GAAG,QAAQ,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;;AAEzC,IAAA,MAAM,OAAO,GAAG,eAAe,EAAE,CAAC;IAElC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,IAAI,EAAE,CAAC;IACtC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,IAAI,EAAE,CAAC;IACtC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,IAAI,EAAE,CAAC;IACtC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,IAAI,EAAE,CAAC;IACtC,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,GAAG,CAAC,IAAI,CAAC,CAAC;AACrC,IAAA,KAAK,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC;IAC5B,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC;AAChC,IAAA,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IACjB,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,OAAO,KAAK,EAAE,CAAC,CAAC;AACnC,IAAA,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,KAAK,EAAE,CAAC;AAC1B,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,CAAC,CAAC;AAC1B,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;AACpB,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,EAAE,CAAC;AAC3B,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,EAAE,CAAC;AAC3B,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,KAAK,CAAC,CAAC;AAC1B,IAAA,KAAK,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;IAEpB,IAAI,IAAI,GAAG,EAAE,CAAC;AACd,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnC,QAAA,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1C,QAAA,IAAI,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1C,IAAI,IAAI,GAAG,CAAC;AACf,SAAA;AACJ,KAAA;AACD,IAAA,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;;AAIG;AACI,eAAe,eAAe,CACjC,WAAoB,EACpB,MAAuB,EAAA;AAEvB,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CACnC,sBAAsB,EACtB,WAAW,EACX,MAAM,CACiB,CAAC;AAChC,CAAC;AAED;;;AAGG;AACI,eAAe,SAAS,CAAC,GAAc,EAAA;AAC1C,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CACjC,cAAc,EACd,GAAG,CACiB,CAAC;AAC7B,CAAC;AAED;;;;;AAKG;AACI,eAAe,SAAS,CAC3B,GAAe,EACf,WAAoB,EACpB,MAAuB,EAAA;AAEvB,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CACjC,cAAc,EACd,GAAG,EACH,sBAAsB,EACtB,WAAW,EACX,MAAM,CACa,CAAC;AAC5B,CAAC;AAED;;;;AAIG;AACI,eAAe,IAAI,CACtB,GAAc,EACd,IAAiB,EAAA;AAEjB,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAC5B,sBAAsB,EACtB,GAAG,EACH,IAAI,CACiB,CAAC;AAC9B,CAAC;AAED;;AAEG;AACI,eAAe,cAAc,GAAA;AAChC,IAAA,MAAM,GAAG,GAAG,MAAM,eAAe,EAAE,CAAC;IACpC,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AAEjD,IAAA,MAAM,GAAG,GAAG;AACR,QAAA,GAAG,EAAE,KAAK;AACV,QAAA,GAAG,EAAE,KAAK;AACV,QAAA,CAAC,EAAE,MAAM;KACZ,CAAC;IAEF,OAAO,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7C,CAAC;AAED;;;;AAIG;AACI,eAAe,YAAY,CAAC,MAAc,EAAA;AAC7C,IAAA,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;IAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AAC1C,IAAA,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC;AACzB,IAAA,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;AAEzC,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE;QAClE,OAAO;AACV,KAAA,CAAC,CAAC;AACP,CAAC;AAED;;;;;AAKG;AACI,eAAe,kBAAkB,CACpC,MAAc,EACd,MAAc,EAAA;IAEd,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACtC,IAAA,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1B,MAAM,sBAAsB,CACxBC,0BAAgD,EAChD,YAAY,CACf,CAAC;AACL,KAAA;IAED,MAAM,GAAG,GAAG,MAAM,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,MAAK;QAC9C,MAAM,sBAAsB,CACxBA,0BAAgD,EAChD,YAAY,CACf,CAAC;AACN,KAAC,CAAC,CAAC;IAEH,IAAI;AACA,QAAA,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,MAAM,EAAE,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,UAAU,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,MAAM,GAAG,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,QAAA,MAAM,aAAa,GAAG,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;;AAGzC,QAAA,MAAM,aAAa,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;AACrE,QAAA,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC9B,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;QAE1C,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CACpD;AACI,YAAA,IAAI,EAAE,OAAO;AACb,YAAA,EAAE,EAAE,EAAE;AACN,YAAA,SAAS,EAAE,aAAa;AACxB,YAAA,cAAc,EAAE,MAAM;AACzB,SAAA,EACD,GAAG,EACH,aAAa,CAChB,CAAC;QAEF,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAClD,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;QACR,MAAM,sBAAsB,CACxBA,0BAAgD,EAChD,SAAS,CACZ,CAAC;AACL,KAAA;AACL,CAAC;AAED;;AAEG;AACI,eAAe,eAAe,GAAA;IACjC,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAC9C;AACI,QAAA,IAAI,EAAE,OAAO;AACb,QAAA,MAAM,EAAE,GAAG;KACd,EACD,IAAI,EACJ,CAAC,OAAO,EAAE,OAAO,CAAC,CACrB,CAAC;AACF,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACpD,CAAC;AAED;;;;AAIG;AACI,eAAe,YAAY,CAAC,OAAoB,EAAA;AACnD,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;QAC7D,UAAU;AACb,KAAA,CAAC,CAAC;AACP,CAAC;AAED;;;;;;AAMG;AACH,eAAe,SAAS,CACpB,OAAkB,EAClB,KAAkB,EAClB,OAAe,EAAA;AAEf,IAAA,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CACjC;AACI,QAAA,IAAI,EAAE,IAAI;AACV,QAAA,IAAI,EAAE,KAAK;AACX,QAAA,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC;KAC1C,EACD,OAAO,EACP,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,EAC9B,KAAK,EACL,CAAC,OAAO,EAAE,OAAO,CAAC,CACrB,CAAC;AACN,CAAC;AAED;;;;AAIG;AACI,eAAe,OAAO,CACzB,OAAkB,EAClB,OAAe,EACf,OAAe,EAAA;IAEf,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;;AAEtD,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;IAChE,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC5D,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CACpD;AACI,QAAA,IAAI,EAAE,OAAO;AACb,QAAA,EAAE,EAAE,IAAI,UAAU,CAAC,EAAE,CAAC;AACzB,KAAA,EACD,UAAU,EACV,WAAW,CACd,CAAC;IAEF,OAAO;QACH,IAAI,EAAE,YAAY,CAAC,IAAI,UAAU,CAAC,aAAa,CAAC,CAAC;AACjD,QAAA,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC;KAC7B,CAAC;AACN,CAAC;AAED;;;;;;AAMG;AACI,eAAe,OAAO,CACzB,OAAkB,EAClB,KAAa,EACb,OAAe,EACf,aAAqB,EAAA;AAErB,IAAA,MAAM,WAAW,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;AAClD,IAAA,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,OAAO,EAAE,cAAc,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;IAC5E,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CACpD;AACI,QAAA,IAAI,EAAE,OAAO;AACb,QAAA,EAAE,EAAE,IAAI,UAAU,CAAC,EAAE,CAAC;AACzB,KAAA,EACD,UAAU,EACV,WAAW,CACd,CAAC;IAEF,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACnD,CAAC;AAED;;;AAGG;AACI,eAAe,UAAU,CAAC,SAAiB,EAAA;AAC9C,IAAA,MAAM,UAAU,GAAgB,MAAM,YAAY,CAAC,SAAS,CAAC,CAAC;AAC9D,IAAA,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;AAC7C,IAAA,OAAO,YAAY,CAAC,SAAS,CAAC,CAAC;AACnC;;;;"}