{"version": 3, "file": "PkceGenerator.mjs", "sources": ["../../../../src/crypto/PkceGenerator.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.pkceNotCreated"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAiBH;AACA,MAAM,sBAAsB,GAAG,EAAE,CAAC;AAElC;;AAEG;AAEH;;AAEG;AACI,eAAe,iBAAiB,CACnC,iBAAqC,EACrC,MAAc,EACd,aAAqB,EAAA;IAErB,iBAAiB,CAAC,mBAAmB,CACjC,iBAAiB,CAAC,iBAAiB,EACnC,aAAa,CAChB,CAAC;IACF,MAAM,YAAY,GAAG,MAAM,CACvB,oBAAoB,EACpB,iBAAiB,CAAC,oBAAoB,EACtC,MAAM,EACN,iBAAiB,EACjB,aAAa,CAChB,CAAC,iBAAiB,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IAC5C,MAAM,aAAa,GAAG,MAAM,WAAW,CACnC,iCAAiC,EACjC,iBAAiB,CAAC,iCAAiC,EACnD,MAAM,EACN,iBAAiB,EACjB,aAAa,CAChB,CAAC,YAAY,EAAE,iBAAiB,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IAC1D,OAAO;AACH,QAAA,QAAQ,EAAE,YAAY;AACtB,QAAA,SAAS,EAAE,aAAa;KAC3B,CAAC;AACN,CAAC;AAED;;;AAGG;AACH,SAAS,oBAAoB,CACzB,iBAAqC,EACrC,MAAc,EACd,aAAqB,EAAA;IAErB,IAAI;;AAEA,QAAA,MAAM,MAAM,GAAe,IAAI,UAAU,CAAC,sBAAsB,CAAC,CAAC;AAClE,QAAA,MAAM,CACF,eAAe,EACf,iBAAiB,CAAC,eAAe,EACjC,MAAM,EACN,iBAAiB,EACjB,aAAa,CAChB,CAAC,MAAM,CAAC,CAAC;;AAEV,QAAA,MAAM,mBAAmB,GAAW,YAAY,CAAC,MAAM,CAAC,CAAC;AACzD,QAAA,OAAO,mBAAmB,CAAC;AAC9B,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACR,QAAA,MAAM,sBAAsB,CAACA,cAAoC,CAAC,CAAC;AACtE,KAAA;AACL,CAAC;AAED;;;AAGG;AACH,eAAe,iCAAiC,CAC5C,gBAAwB,EACxB,iBAAqC,EACrC,MAAc,EACd,aAAqB,EAAA;IAErB,iBAAiB,CAAC,mBAAmB,CACjC,iBAAiB,CAAC,iCAAiC,EACnD,aAAa,CAChB,CAAC;IACF,IAAI;;QAEA,MAAM,sBAAsB,GAAG,MAAM,WAAW,CAC5C,YAAY,EACZ,iBAAiB,CAAC,YAAY,EAC9B,MAAM,EACN,iBAAiB,EACjB,aAAa,CAChB,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;;QAEtD,OAAO,YAAY,CAAC,IAAI,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC;AAC/D,KAAA;AAAC,IAAA,OAAO,CAAC,EAAE;AACR,QAAA,MAAM,sBAAsB,CAACA,cAAoC,CAAC,CAAC;AACtE,KAAA;AACL;;;;"}