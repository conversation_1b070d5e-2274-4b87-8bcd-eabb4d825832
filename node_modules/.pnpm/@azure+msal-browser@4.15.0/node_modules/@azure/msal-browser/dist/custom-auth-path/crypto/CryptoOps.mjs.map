{"version": 3, "file": "CryptoOps.mjs", "sources": ["../../../../src/crypto/CryptoOps.ts"], "sourcesContent": [null], "names": ["BrowserCrypto.validateCryptoAvailable", "BrowserCrypto.createNewGuid", "BrowserCrypto.generateKeyPair", "BrowserCrypto.exportJwk", "BrowserCrypto.importJwk", "BrowserAuthErrorCodes.cryptoKeyNotFound", "BrowserCrypto.sign", "BrowserCrypto.hashString"], "mappings": ";;;;;;;;;;AAAA;;;AAGG;AAkCH;;;AAGG;MACU,SAAS,CAAA;AAalB,IAAA,WAAA,CACI,MAAc,EACd,iBAAsC,EACtC,wBAAkC,EAAA;AAElC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;;AAErB,QAAAA,uBAAqC,CACjC,wBAAwB,IAAI,KAAK,CACpC,CAAC;QACF,IAAI,CAAC,KAAK,GAAG,IAAI,kBAAkB,CAAgB,IAAI,CAAC,MAAM,CAAC,CAAC;AAChE,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;KAC9C;AAED;;;AAGG;IACH,aAAa,GAAA;AACT,QAAA,OAAOC,aAA2B,EAAE,CAAC;KACxC;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,KAAa,EAAA;AACtB,QAAA,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC;KAC9B;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,KAAa,EAAA;AACtB,QAAA,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC;KAC9B;AAED;;;AAGG;AACH,IAAA,eAAe,CAAC,KAAa,EAAA;AACzB,QAAA,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC;KAC3B;AAED;;;;AAIG;AACH,IAAA,SAAS,CAAC,QAAgB,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;KAClE;AAED;;;AAGG;IACH,MAAM,sBAAsB,CACxB,OAAoC,EAAA;AAEpC,QAAA,MAAM,yBAAyB,GAC3B,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,CACpC,iBAAiB,CAAC,gCAAgC,EAClD,OAAO,CAAC,aAAa,CACxB,CAAC;;AAGN,QAAA,MAAM,OAAO,GAAkB,MAAMC,eAA6B,CAC9D,SAAS,CAAC,WAAW,EACrB,SAAS,CAAC,cAAc,CAC3B,CAAC;;QAGF,MAAM,YAAY,GAAe,MAAMC,SAAuB,CAC1D,OAAO,CAAC,SAAS,CACpB,CAAC;AAEF,QAAA,MAAM,kBAAkB,GAAe;YACnC,CAAC,EAAE,YAAY,CAAC,CAAC;YACjB,GAAG,EAAE,YAAY,CAAC,GAAG;YACrB,CAAC,EAAE,YAAY,CAAC,CAAC;SACpB,CAAC;AAEF,QAAA,MAAM,eAAe,GACjB,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;QAC9C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;;QAG7D,MAAM,aAAa,GAAe,MAAMA,SAAuB,CAC3D,OAAO,CAAC,UAAU,CACrB,CAAC;;AAEF,QAAA,MAAM,uBAAuB,GACzB,MAAMC,SAAuB,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;;AAGlE,QAAA,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE;AACpC,YAAA,UAAU,EAAE,uBAAuB;YACnC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,aAAa,EAAE,OAAO,CAAC,qBAAqB;YAC5C,UAAU,EAAE,OAAO,CAAC,kBAAkB;AACzC,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,yBAAyB,EAAE;YAC3B,yBAAyB,CAAC,GAAG,CAAC;AAC1B,gBAAA,OAAO,EAAE,IAAI;AAChB,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,OAAO,aAAa,CAAC;KACxB;AAED;;;AAGG;IACH,MAAM,qBAAqB,CAAC,GAAW,EAAA;QACnC,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACnD,QAAA,IAAI,QAAQ,EAAE;AACV,YAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,oBAAoB,CAC5C,CAAC;AACL,SAAA;KACJ;AAED;;AAEG;AACH,IAAA,MAAM,aAAa,GAAA;;AAEf,QAAA,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;AAE3B;;;AAGG;QACH,IAAI;AACA,YAAA,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;AACnC,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,KAAK,EAAE;gBACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAAwC,qCAAA,EAAA,CAAC,CAAC,OAAO,CAAE,CAAA,CACtD,CAAC;AACL,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,6CAA6C,CAChD,CAAC;AACL,aAAA;AAED,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;KACJ;AAED;;;;AAIG;IACH,MAAM,OAAO,CACT,OAA0B,EAC1B,GAAW,EACX,UAAuB,EACvB,aAAsB,EAAA;AAEtB,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,CAC/D,iBAAiB,CAAC,iBAAiB,EACnC,aAAa,CAChB,CAAC;QACF,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEpD,IAAI,CAAC,aAAa,EAAE;AAChB,YAAA,MAAM,sBAAsB,CACxBC,iBAAuC,CAC1C,CAAC;AACL,SAAA;;QAGD,MAAM,YAAY,GAAG,MAAMF,SAAuB,CAC9C,aAAa,CAAC,SAAS,CAC1B,CAAC;AACF,QAAA,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;;AAE/D,QAAA,MAAM,sBAAsB,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;;AAEvE,QAAA,MAAM,SAAS,GAAG,UAAU,CAAC,kBAAkB,CAAC;YAC5C,GAAG,UAAU,EAAE,MAAM;YACrB,GAAG,EAAE,YAAY,CAAC,GAAG;AACrB,YAAA,GAAG,EAAE,sBAAsB;AAC9B,SAAA,CAAC,CAAC;AAEH,QAAA,MAAM,gBAAgB,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;;QAG9C,OAAO,CAAC,GAAG,GAAG;AACV,YAAA,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;SACtC,CAAC;QACF,MAAM,cAAc,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;;AAG1D,QAAA,MAAM,WAAW,GAAG,CAAA,EAAG,gBAAgB,CAAI,CAAA,EAAA,cAAc,EAAE,CAAC;;AAG5D,QAAA,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAClC,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAChD,QAAA,MAAM,eAAe,GAAG,MAAMG,IAAkB,CAC5C,aAAa,CAAC,UAAU,EACxB,WAAW,CACd,CAAC;QACF,MAAM,gBAAgB,GAAG,YAAY,CAAC,IAAI,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;AAEvE,QAAA,MAAM,SAAS,GAAG,CAAA,EAAG,WAAW,CAAI,CAAA,EAAA,gBAAgB,EAAE,CAAC;AAEvD,QAAA,IAAI,kBAAkB,EAAE;YACpB,kBAAkB,CAAC,GAAG,CAAC;AACnB,gBAAA,OAAO,EAAE,IAAI;AAChB,aAAA,CAAC,CAAC;AACN,SAAA;AAED,QAAA,OAAO,SAAS,CAAC;KACpB;AAED;;;AAGG;IACH,MAAM,UAAU,CAAC,SAAiB,EAAA;AAC9B,QAAA,OAAOC,UAAwB,CAAC,SAAS,CAAC,CAAC;KAC9C;;AA3Oc,SAAA,CAAA,cAAc,GAAoB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACrD,SAAW,CAAA,WAAA,GAAY,IAAI,CAAC;AA6O/C,SAAS,qBAAqB,CAAC,GAAW,EAAA;AACtC,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AACxD;;;;"}