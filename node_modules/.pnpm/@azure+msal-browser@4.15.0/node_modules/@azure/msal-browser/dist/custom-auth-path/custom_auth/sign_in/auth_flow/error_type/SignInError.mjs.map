{"version": 3, "file": "SignInError.mjs", "sources": ["../../../../../../../src/custom_auth/sign_in/auth_flow/error_type/SignInError.ts"], "sourcesContent": [null], "names": ["CustomAuthApiErrorCode.USER_NOT_FOUND"], "mappings": ";;;;;AAAA;;;AAGG;AAKG,MAAO,WAAY,SAAQ,mBAAmB,CAAA;AAChD;;;AAGG;IACH,cAAc,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,KAAKA,cAAqC,CAAC;KACzE;AAED;;;AAGG;IACH,iBAAiB,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;KACpC;AAED;;;AAGG;IACH,mBAAmB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,wBAAwB,EAAE,CAAC;KAC1C;AAED;;;AAGG;IACH,0BAA0B,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,+BAA+B,EAAE,CAAC;KACjD;AAED;;;AAGG;IACH,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;KACjC;AACJ,CAAA;AAEK,MAAO,yBAA0B,SAAQ,mBAAmB,CAAA;AAC9D;;;AAGG;IACH,iBAAiB,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,wBAAwB,EAAE,CAAC;KAC1C;AACJ,CAAA;AAEK,MAAO,qBAAsB,SAAQ,mBAAmB,CAAA;AAC1D;;;AAGG;IACH,aAAa,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;KACpC;AACJ,CAAA;AAEK,MAAO,qBAAsB,SAAQ,mBAAmB,CAAA;AAC1D;;;AAGG;IACH,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;KACjC;AACJ;;;;"}