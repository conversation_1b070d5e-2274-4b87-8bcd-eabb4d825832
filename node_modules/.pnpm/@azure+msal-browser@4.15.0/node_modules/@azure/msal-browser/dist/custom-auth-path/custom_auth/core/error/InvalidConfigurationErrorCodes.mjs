/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
const MissingConfiguration = "missing_configuration";
const InvalidAuthority = "invalid_authority";
const InvalidChallengeType = "invalid_challenge_type";

export { InvalidAuthority, InvalidChallengeType, MissingConfiguration };
//# sourceMappingURL=InvalidConfigurationErrorCodes.mjs.map
