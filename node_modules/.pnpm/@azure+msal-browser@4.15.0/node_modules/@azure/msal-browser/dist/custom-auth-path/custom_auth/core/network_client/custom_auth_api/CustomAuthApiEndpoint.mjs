/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
const SIGNIN_INITIATE = "/oauth2/v2.0/initiate";
const SIGNIN_CHALLENGE = "/oauth2/v2.0/challenge";
const SIGNIN_TOKEN = "/oauth2/v2.0/token";
const SIGNUP_START = "/signup/v1.0/start";
const SIGNUP_CHALLENGE = "/signup/v1.0/challenge";
const SIGNUP_CONTINUE = "/signup/v1.0/continue";
const RESET_PWD_START = "/resetpassword/v1.0/start";
const RESET_PWD_CHALLENGE = "/resetpassword/v1.0/challenge";
const RESET_PWD_CONTINUE = "/resetpassword/v1.0/continue";
const RESET_PWD_SUBMIT = "/resetpassword/v1.0/submit";
const RESET_PWD_POLL = "/resetpassword/v1.0/poll_completion";

export { RESET_PWD_CHALLENGE, RESET_PWD_CONTINUE, RESET_PWD_POLL, RESET_PWD_START, RESET_PWD_SUBMIT, SIGNIN_CHALLENGE, SIGNIN_INITIATE, SIGNIN_TOKEN, SIGNUP_CHALLENGE, SIGNUP_CONTINUE, SIGNUP_START };
//# sourceMappingURL=CustomAuthApiEndpoint.mjs.map
