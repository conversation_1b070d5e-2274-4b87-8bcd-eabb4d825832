/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { CustomAuthError } from './CustomAuthError.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
class ParsedUrlError extends CustomAuthError {
    constructor(error, message, correlationId) {
        super(error, message, correlationId);
        Object.setPrototypeOf(this, ParsedUrlError.prototype);
    }
}

export { ParsedUrlError };
//# sourceMappingURL=ParsedUrlError.mjs.map
