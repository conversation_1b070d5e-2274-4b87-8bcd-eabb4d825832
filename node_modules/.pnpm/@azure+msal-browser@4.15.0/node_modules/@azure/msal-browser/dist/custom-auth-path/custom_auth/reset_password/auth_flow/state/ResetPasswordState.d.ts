import { AuthFlowActionRequiredStateBase } from "../../../core/auth_flow/AuthFlowState.js";
import { ResetPasswordStateParameters } from "./ResetPasswordStateParameters.js";
export declare abstract class ResetPasswordState<TParameters extends ResetPasswordStateParameters> extends AuthFlowActionRequiredStateBase<TParameters> {
    constructor(stateParameters: TParameters);
}
//# sourceMappingURL=ResetPasswordState.d.ts.map