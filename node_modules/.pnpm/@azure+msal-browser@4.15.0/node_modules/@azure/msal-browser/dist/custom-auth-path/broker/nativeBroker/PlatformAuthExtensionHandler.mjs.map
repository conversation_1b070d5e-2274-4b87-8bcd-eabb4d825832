{"version": 3, "file": "PlatformAuthExtensionHandler.mjs", "sources": ["../../../../../src/broker/nativeBroker/PlatformAuthExtensionHandler.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.nativeHandshakeTimeout", "BrowserAuthErrorCodes.nativeExtensionNotInstalled"], "mappings": ";;;;;;;;;AAAA;;;AAGG;MAoCU,4BAA4B,CAAA;AAcrC,IAAA,WAAA,CACI,MAAc,EACd,kBAA0B,EAC1B,iBAAqC,EACrC,WAAoB,EAAA;AAEpB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AAC7C,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;AACpC,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;AAC3C,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,cAAc,GAAG,iBAAiB,CAAC,gBAAgB,CACpD,iBAAiB,CAAC,6BAA6B,CAClD,CAAC;AACF,QAAA,IAAI,CAAC,gBAAgB;YACjB,qBAAqB,CAAC,2BAA2B,CAAC;KACzD;AAED;;;AAGG;IACH,MAAM,WAAW,CACb,OAA4B,EAAA;QAE5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,wBAAwB,CAAC,CAAC;;AAGpE,QAAA,MAAM,WAAW,GAA+B;YAC5C,MAAM,EAAE,qBAAqB,CAAC,QAAQ;AACtC,YAAA,OAAO,EAAE,OAAO;SACnB,CAAC;AAEF,QAAA,MAAM,GAAG,GAA2B;YAChC,OAAO,EAAE,qBAAqB,CAAC,UAAU;YACzC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,aAAa,EAAE;AAC3B,YAAA,IAAI,EAAE,WAAW;SACpB,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB,GAAG,yCAAyC,CACpE,CAAC;AACF,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAChB,IAAI,CAAC,gBAAgB;YACjB,CAA4C,yCAAA,EAAA,IAAI,CAAC,SAAS,CACtD,GAAG,CACN,CAAA,CAAE,CACV,CAAC;QACF,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAE3C,MAAM,QAAQ,GAAW,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AAC3D,YAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;AAC5D,SAAC,CAAC,CAAC;QAEH,MAAM,iBAAiB,GACnB,IAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,CAAC;AAElD,QAAA,OAAO,iBAAiB,CAAC;KAC5B;AAED;;;;;;AAMG;IACH,aAAa,cAAc,CACvB,MAAc,EACd,kBAA0B,EAC1B,iBAAqC,EAAA;AAErC,QAAA,MAAM,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAEtE,IAAI;AACA,YAAA,MAAM,iBAAiB,GAAG,IAAI,4BAA4B,CACtD,MAAM,EACN,kBAAkB,EAClB,iBAAiB,EACjB,qBAAqB,CAAC,sBAAsB,CAC/C,CAAC;AACF,YAAA,MAAM,iBAAiB,CAAC,oBAAoB,EAAE,CAAC;AAC/C,YAAA,OAAO,iBAAiB,CAAC;AAC5B,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;YAER,MAAM,cAAc,GAAG,IAAI,4BAA4B,CACnD,MAAM,EACN,kBAAkB,EAClB,iBAAiB,CACpB,CAAC;AACF,YAAA,MAAM,cAAc,CAAC,oBAAoB,EAAE,CAAC;AAC5C,YAAA,OAAO,cAAc,CAAC;AACzB,SAAA;KACJ;AAED;;AAEG;AACK,IAAA,MAAM,oBAAoB,GAAA;QAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB,GAAG,iCAAiC,CAC5D,CAAC;;AAEF,QAAA,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAE/D,QAAA,MAAM,GAAG,GAA2B;YAChC,OAAO,EAAE,qBAAqB,CAAC,UAAU;YACzC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,aAAa,EAAE;AAC3B,YAAA,IAAI,EAAE;gBACF,MAAM,EAAE,qBAAqB,CAAC,gBAAgB;AACjD,aAAA;SACJ,CAAC;AACF,QAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;YACpB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,2BAA2B,EAAE,IAAI,CAAC,kBAAkB;AACvD,SAAA,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,KAAK,KAAI;AAC5C,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACjC,SAAC,CAAC;AAEF,QAAA,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QAEpE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACnC,YAAA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YACjE,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAK;AACpC;;;AAGG;gBACH,MAAM,CAAC,mBAAmB,CACtB,SAAS,EACT,IAAI,CAAC,cAAc,EACnB,KAAK,CACR,CAAC;AACF,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAClC,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAClC,gBAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;AACpB,oBAAA,0BAA0B,EAAE,IAAI;AAChC,oBAAA,OAAO,EAAE,KAAK;AACjB,iBAAA,CAAC,CAAC;gBACH,MAAM,CACF,sBAAsB,CAClBA,sBAA4C,CAC/C,CACJ,CAAC;gBACF,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACnD,aAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAChC,SAAC,CAAC,CAAC;KACN;AAED;;;AAGG;AACK,IAAA,eAAe,CAAC,KAAmB,EAAA;QACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,2BAA2B,CAAC,CAAC;;AAEvE,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,EAAE;YACzB,OAAO;AACV,SAAA;AAED,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;QAE3B,IACI,CAAC,OAAO,CAAC,OAAO;AAChB,YAAA,OAAO,CAAC,OAAO,KAAK,qBAAqB,CAAC,UAAU,EACtD;YACE,OAAO;AACV,SAAA;QAED,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE;YACjE,OAAO;AACV,SAAA;QAED,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,qBAAqB,CAAC,gBAAgB,EAAE;AAChE,YAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CACjD,OAAO,CAAC,UAAU,CACrB,CAAC;AACF;;;AAGG;YACH,IAAI,CAAC,iBAAiB,EAAE;AACpB,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB;AACjB,oBAAA,CAAA,uDAAA,EAA0D,OAAO,CAAC,UAAU,CAAA,CAAE,CACrF,CAAC;gBACF,OAAO;AACV,aAAA;;AAGD,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,OAAO,CAAC,WAAW;AACf,kBAAE,CAAA,mBAAA,EAAsB,OAAO,CAAC,WAAW,CAAgB,cAAA,CAAA;kBACzD,wBAAwB,CACjC,CAAC;AACF,YAAA,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7B,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YAClC,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;AAClE,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;AACpB,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,kBAAkB,EAAE,KAAK;AAC5B,aAAA,CAAC,CAAC;YACH,iBAAiB,CAAC,MAAM,CACpB,sBAAsB,CAClBC,2BAAiD,CACpD,CACJ,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;AACK,IAAA,gBAAgB,CAAC,KAAmB,EAAA;QACxC,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB,GAAG,6BAA6B,CACxD,CAAC;AACF,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;AAE3B,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACxD,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CACjD,OAAO,CAAC,UAAU,CACrB,CAAC;QAEF,IAAI;AACA,YAAA,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AAEnC,YAAA,IAAI,MAAM,KAAK,qBAAqB,CAAC,QAAQ,EAAE;gBAC3C,IAAI,CAAC,QAAQ,EAAE;oBACX,OAAO;AACV,iBAAA;AACD,gBAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;AACvC,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB;AACjB,oBAAA,6CAA6C,CACpD,CAAC;AACF,gBAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAChB,IAAI,CAAC,gBAAgB;oBACjB,CAAgD,6CAAA,EAAA,IAAI,CAAC,SAAS,CAC1D,QAAQ,CACX,CAAA,CAAE,CACV,CAAC;AACF,gBAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE;AAC/B,oBAAA,QAAQ,CAAC,MAAM,CACX,qBAAqB,CACjB,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,WAAW,EACpB,QAAQ,CAAC,GAAG,CACf,CACJ,CAAC;AACL,iBAAA;qBAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;AACxB,oBAAA,IACI,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;AACvB,wBAAA,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,EAChC;wBACE,QAAQ,CAAC,MAAM,CACX,qBAAqB,CACjB,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EACvB,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,EAC9B,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CACzB,CACJ,CAAC;AACL,qBAAA;AAAM,yBAAA;AACH,wBAAA,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrC,qBAAA;AACJ,iBAAA;AAAM,qBAAA;oBACH,MAAM,eAAe,CACjB,cAAc,CAAC,eAAe,EAC9B,gCAAgC,CACnC,CAAC;AACL,iBAAA;gBACD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC7C,aAAA;AAAM,iBAAA,IAAI,MAAM,KAAK,qBAAqB,CAAC,iBAAiB,EAAE;gBAC3D,IAAI,CAAC,iBAAiB,EAAE;AACpB,oBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,IAAI,CAAC,gBAAgB;AACjB,wBAAA,CAAA,wDAAA,EAA2D,OAAO,CAAC,UAAU,CAAA,CAAE,CACtF,CAAC;oBACF,OAAO;AACV,iBAAA;AACD,gBAAA,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7B,gBAAA,MAAM,CAAC,mBAAmB,CACtB,SAAS,EACT,IAAI,CAAC,cAAc,EACnB,KAAK,CACR,CAAC;AACF,gBAAA,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;gBACvC,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;AAC7C,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,IAAI,CAAC,gBAAgB;AACjB,oBAAA,CAAA,8CAAA,EAAiD,IAAI,CAAC,WAAW,CAAA,CAAE,CAC1E,CAAC;AACF,gBAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;AACpB,oBAAA,kBAAkB,EAAE,IAAI;AACxB,oBAAA,OAAO,EAAE,IAAI;AAChB,iBAAA,CAAC,CAAC;gBAEH,iBAAiB,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACtD,aAAA;;AAEJ,SAAA;AAAC,QAAA,OAAO,GAAG,EAAE;AACV,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC/D,IAAI,CAAC,MAAM,CAAC,QAAQ,CAChB,CAA8C,2CAAA,EAAA,GAAa,CAAE,CAAA,CAChE,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAmB,gBAAA,EAAA,KAAK,CAAE,CAAA,CAAC,CAAC;AAEjD,YAAA,IAAI,QAAQ,EAAE;AACV,gBAAA,QAAQ,CAAC,MAAM,CAAC,GAAgB,CAAC,CAAC;AACrC,aAAA;AAAM,iBAAA,IAAI,iBAAiB,EAAE;AAC1B,gBAAA,iBAAiB,CAAC,MAAM,CAAC,GAAgB,CAAC,CAAC;AAC9C,aAAA;AACJ,SAAA;KACJ;AAED;;;AAGG;AACK,IAAA,8BAA8B,CAClC,QAAgB,EAAA;AAEhB,QAAA,IACI,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC;AACvC,YAAA,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC;AACnC,YAAA,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC;AACtC,YAAA,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC;AAClC,YAAA,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC;AAChC,YAAA,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,EACvC;AACE,YAAA,OAAO,QAAgC,CAAC;AAC3C,SAAA;AAAM,aAAA;YACH,MAAM,eAAe,CACjB,cAAc,CAAC,eAAe,EAC9B,uCAAuC,CAC1C,CAAC;AACL,SAAA;KACJ;AAED;;;AAGG;IACH,cAAc,GAAA;QACV,OAAO,IAAI,CAAC,WAAW,CAAC;KAC3B;AAED;;;AAGG;IACH,mBAAmB,GAAA;QACf,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAChC;IAED,gBAAgB,GAAA;QACZ,OAAO,IAAI,CAAC,cAAc,EAAE;AACxB,YAAA,qBAAqB,CAAC,sBAAsB;AAC5C,cAAE,QAAQ;AACV,cAAE,IAAI,CAAC,cAAc,EAAE,EAAE,MAAM;AAC/B,kBAAE,SAAS;kBACT,SAAS,CAAC;KACnB;AACJ;;;;"}