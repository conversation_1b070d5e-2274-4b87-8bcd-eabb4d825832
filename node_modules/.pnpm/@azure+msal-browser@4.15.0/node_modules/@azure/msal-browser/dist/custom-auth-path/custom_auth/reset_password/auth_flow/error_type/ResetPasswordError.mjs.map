{"version": 3, "file": "ResetPasswordError.mjs", "sources": ["../../../../../../../src/custom_auth/reset_password/auth_flow/error_type/ResetPasswordError.ts"], "sourcesContent": [null], "names": ["CustomAuthApiErrorCode.PASSWORD_RESET_TIMEOUT", "CustomAuthApiErrorCode.PASSWORD_CHANGE_FAILED"], "mappings": ";;;;;;AAAA;;;AAGG;AAMG,MAAO,kBAAmB,SAAQ,mBAAmB,CAAA;AACvD;;;AAGG;IACH,cAAc,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;KACrC;AAED;;;AAGG;IACH,iBAAiB,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;KACpC;AAED;;;AAGG;IACH,0BAA0B,GAAA;AACtB,QAAA,OAAO,IAAI,CAAC,+BAA+B,EAAE,CAAC;KACjD;AAED;;;AAGG;IACH,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;KACjC;AACJ,CAAA;AAEK,MAAO,gCAAiC,SAAQ,mBAAmB,CAAA;AACrE;;;AAGG;IACH,iBAAiB,GAAA;QACb,QACI,IAAI,CAAC,yBAAyB,EAAE,IAAI,IAAI,CAAC,wBAAwB,EAAE,EACrE;KACL;AAED;;;AAGG;IACH,qBAAqB,GAAA;AACjB,QAAA,QACI,IAAI,CAAC,SAAS,YAAY,kBAAkB;AAC5C,aAAC,IAAI,CAAC,SAAS,CAAC,KAAK;AACjB,gBAAAA,sBAA6C;gBAC7C,IAAI,CAAC,SAAS,CAAC,KAAK;AAChB,oBAAAC,sBAA6C,CAAC,EACxD;KACL;AACJ,CAAA;AAEK,MAAO,4BAA6B,SAAQ,mBAAmB,CAAA;AACjE;;;AAGG;IACH,aAAa,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;KACpC;AAED;;;AAGG;IACH,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;KACjC;AACJ,CAAA;AAEK,MAAO,4BAA6B,SAAQ,mBAAmB,CAAA;AACjE;;;AAGG;IACH,kBAAkB,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;KACjC;AACJ;;;;"}