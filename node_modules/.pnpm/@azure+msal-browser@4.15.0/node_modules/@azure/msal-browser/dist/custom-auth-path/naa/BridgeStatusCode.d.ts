export declare const BridgeStatusCode: {
    readonly UserInteractionRequired: "USER_INTERACTION_REQUIRED";
    readonly UserCancel: "USER_CANCEL";
    readonly NoNetwork: "NO_NETWORK";
    readonly TransientError: "TRANSIENT_ERROR";
    readonly PersistentError: "PERSISTENT_ERROR";
    readonly Disabled: "DISABLED";
    readonly AccountUnavailable: "ACCOUNT_UNAVAILABLE";
    readonly NestedAppAuthUnavailable: "NESTED_APP_AUTH_UNAVAILABLE";
};
export type BridgeStatusCode = (typeof BridgeStatusCode)[keyof typeof BridgeStatusCode];
//# sourceMappingURL=BridgeStatusCode.d.ts.map