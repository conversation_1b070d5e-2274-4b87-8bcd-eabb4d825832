import { IPerformanceClient, Logger, PkceCodes } from "@azure/msal-common/browser";
/**
 * This file defines APIs to generate PKCE codes and code verifiers.
 */
/**
 * Generates PKCE Codes. See the RFC for more information: https://tools.ietf.org/html/rfc7636
 */
export declare function generatePkceCodes(performanceClient: IPerformanceClient, logger: Logger, correlationId: string): Promise<PkceCodes>;
//# sourceMappingURL=PkceGenerator.d.ts.map