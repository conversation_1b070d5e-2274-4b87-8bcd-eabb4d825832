{"version": 3, "file": "CustomAuthStandardController.mjs", "sources": ["../../../../../src/custom_auth/controller/CustomAuthStandardController.ts"], "sourcesContent": [null], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;AAGG;AAuDH;;AAEG;AACG,MAAO,4BACT,SAAQ,kBAAkB,CAAA;AAU1B;;;;AAIG;IACH,WACI,CAAA,gBAA4C,EAC5C,mBAA0C,EAAA;QAE1C,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAExB,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oDAAoD,CACvD,CAAC;YACF,MAAM,IAAI,2BAA2B,EAAE,CAAC;AAC3C,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAC3B,kBAAkB,CAAC,GAAG,EACtB,kBAAkB,CAAC,OAAO,CAC7B,CAAC;AACF,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,mBAAmB,EAAE,CAAC;AAE/D,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,mBAAmB,CACpC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,EACpC,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,eAAe,CACpD,CAAC;AAEF,QAAA,MAAM,wBAAwB,GAAG,IAAI,iCAAiC,CAClE,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,iBAAiB,EACtB,mBAAmB;AACf,YAAA,IAAI,mBAAmB,CACnB,IAAI,CAAC,SAAS,CAAC,sBAAsB,EAAE,EACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,EACnC,IAAI,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CACnC,EACL,IAAI,CAAC,SAAS,CACjB,CAAC;QAEF,IAAI,CAAC,YAAY,GAAG,wBAAwB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,GAAG,wBAAwB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAClE,QAAA,IAAI,CAAC,mBAAmB;AACpB,YAAA,wBAAwB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QACzD,IAAI,CAAC,WAAW,GAAG,wBAAwB,CAAC,MAAM,CAC9C,2BAA2B,CAC9B,CAAC;KACL;AAED;;;;AAIG;AACH,IAAA,iBAAiB,CACb,sBAA+C,EAAA;QAE/C,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;QACpE,IAAI;YACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,EAAE,aAAa,CAAC,CAAC;YAEpE,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;AAElE,YAAA,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,EAAE,aAAa,CAAC,CAAC;gBAE1D,OAAO,IAAI,gBAAgB,CACvB,IAAI,qBAAqB,CACrB,OAAO,EACP,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,aAAa,CAChB,CACJ,CAAC;AACL,aAAA;AAED,YAAA,MAAM,IAAI,yBAAyB,CAAC,aAAa,CAAC,CAAC;AACtD,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAChB,CAAqD,kDAAA,EAAA,KAAK,CAAE,CAAA,EAC5D,aAAa,CAChB,CAAC;AAEF,YAAA,OAAO,gBAAgB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAClD,SAAA;KACJ;AAED;;;;AAIG;IACH,MAAM,MAAM,CAAC,YAA0B,EAAA;QACnC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAE1D,IAAI;AACA,YAAA,kCAAkC,CAC9B,cAAc,EACd,YAAY,EACZ,aAAa,CAChB,CAAC;YAEF,8BAA8B,CAC1B,uBAAuB,EACvB,YAAY,CAAC,QAAQ,EACrB,aAAa,CAChB,CAAC;AACF,YAAA,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;;AAG1C,YAAA,MAAM,iBAAiB,GAAsB;AACzC,gBAAA,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ;AAC7C,gBAAA,aAAa,EAAE,aAAa;gBAC5B,aAAa,EACT,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,cAAc,IAAI,EAAE;gBACzD,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,QAAQ,EAAE,YAAY,CAAC,QAAQ;aAClC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,CACI,sBAAA,EAAA,CAAC,CAAC,YAAY,CAAC,QAAQ,GAAG,MAAM,GAAG,SACvC,CAAY,UAAA,CAAA,EACZ,aAAa,CAChB,CAAC;YAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAC7C,iBAAiB,CACpB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;AAE5D,YAAA,IAAI,WAAW,CAAC,IAAI,KAAK,6BAA6B,EAAE;;gBAEpD,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4BAA4B,EAC5B,aAAa,CAChB,CAAC;AAEF,gBAAA,OAAO,IAAI,YAAY,CACnB,IAAI,uBAAuB,CAAC;oBACxB,aAAa,EAAE,WAAW,CAAC,aAAa;oBACxC,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;oBAChD,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,IAAI,CAAC,gBAAgB;oBAC7B,YAAY,EAAE,IAAI,CAAC,YAAY;oBAC/B,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;oBAC/B,UAAU,EAAE,WAAW,CAAC,UAAU;AAClC,oBAAA,MAAM,EAAE,YAAY,CAAC,MAAM,IAAI,EAAE;AACpC,iBAAA,CAAC,CACL,CAAC;AACL,aAAA;AAAM,iBAAA,IACH,WAAW,CAAC,IAAI,KAAK,qCAAqC,EAC5D;;gBAEE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gCAAgC,EAChC,aAAa,CAChB,CAAC;AAEF,gBAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;oBACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wEAAwE,EACxE,aAAa,CAChB,CAAC;AAEF,oBAAA,OAAO,IAAI,YAAY,CACnB,IAAI,2BAA2B,CAAC;wBAC5B,aAAa,EAAE,WAAW,CAAC,aAAa;wBACxC,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;wBAChD,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,MAAM,EAAE,IAAI,CAAC,gBAAgB;wBAC7B,YAAY,EAAE,IAAI,CAAC,YAAY;wBAC/B,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;AAC/B,wBAAA,MAAM,EAAE,YAAY,CAAC,MAAM,IAAI,EAAE;AACpC,qBAAA,CAAC,CACL,CAAC;AACL,iBAAA;gBAED,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,kCAAkC,EAClC,aAAa,CAChB,CAAC;;AAGF,gBAAA,MAAM,oBAAoB,GAA+B;AACrD,oBAAA,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ;AAC7C,oBAAA,aAAa,EAAE,aAAa;oBAC5B,aAAa,EACT,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,cAAc,IAAI,EAAE;AACzD,oBAAA,MAAM,EAAE,YAAY,CAAC,MAAM,IAAI,EAAE;oBACjC,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;oBAChD,QAAQ,EAAE,YAAY,CAAC,QAAQ;oBAC/B,QAAQ,EAAE,YAAY,CAAC,QAAQ;iBAClC,CAAC;gBAEF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAC1D,oBAAoB,CACvB,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,yBAAyB,EAAE,aAAa,CAAC,CAAC;gBAE9D,MAAM,WAAW,GAAG,IAAI,qBAAqB,CACzC,eAAe,CAAC,oBAAoB,CAAC,OAAO,EAC5C,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,aAAa,CAChB,CAAC;gBAEF,OAAO,IAAI,YAAY,CACnB,IAAI,oBAAoB,EAAE,EAC1B,WAAW,CACd,CAAC;AACL,aAAA;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,kDAAkD,EAClD,aAAa,CAChB,CAAC;AAEF,YAAA,MAAM,IAAI,eAAe,CACrB,4BAA4B,EAC5B,aAAa,CAChB,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAChB,CAA8C,2CAAA,EAAA,KAAK,CAAE,CAAA,EACrD,aAAa,CAChB,CAAC;AAEF,YAAA,OAAO,YAAY,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAC9C,SAAA;KACJ;AAED;;;;AAIG;IACH,MAAM,MAAM,CAAC,YAA0B,EAAA;QACnC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAE1D,IAAI;AACA,YAAA,kCAAkC,CAC9B,cAAc,EACd,YAAY,EACZ,aAAa,CAChB,CAAC;YAEF,8BAA8B,CAC1B,uBAAuB,EACvB,YAAY,CAAC,QAAQ,EACrB,aAAa,CAChB,CAAC;AACF,YAAA,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;YAE1C,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,wBACI,CAAC,CAAC,YAAY,CAAC,QAAQ;AACnB,kBAAE,CACI,MAAA,EAAA,CAAC,CAAC,YAAY,CAAC,UAAU;AACrB,sBAAE,yBAAyB;sBACzB,UACV,CAAE,CAAA;AACJ,kBAAE,EACV,CAAA,CAAA,CAAG,EACH,aAAa,CAChB,CAAC;YAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;AAC9C,gBAAA,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ;AAC7C,gBAAA,aAAa,EAAE,aAAa;gBAC5B,aAAa,EACT,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,cAAc,IAAI,EAAE;gBACzD,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,UAAU,EAAE,YAAY,CAAC,UAAU;AACtC,aAAA,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;AAE5D,YAAA,IAAI,WAAW,CAAC,IAAI,KAAK,iCAAiC,EAAE;;gBAExD,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4BAA4B,EAC5B,aAAa,CAChB,CAAC;AAEF,gBAAA,OAAO,IAAI,YAAY,CACnB,IAAI,uBAAuB,CAAC;oBACxB,aAAa,EAAE,WAAW,CAAC,aAAa;oBACxC,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;oBAChD,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,IAAI,CAAC,gBAAgB;oBAC7B,YAAY,EAAE,IAAI,CAAC,YAAY;oBAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;oBAC/B,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;oBAC/B,UAAU,EAAE,WAAW,CAAC,UAAU;oBAClC,kBAAkB,EAAE,WAAW,CAAC,QAAQ;AAC3C,iBAAA,CAAC,CACL,CAAC;AACL,aAAA;AAAM,iBAAA,IACH,WAAW,CAAC,IAAI,KAAK,qCAAqC,EAC5D;;gBAEE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gCAAgC,EAChC,aAAa,CAChB,CAAC;AAEF,gBAAA,OAAO,IAAI,YAAY,CACnB,IAAI,2BAA2B,CAAC;oBAC5B,aAAa,EAAE,WAAW,CAAC,aAAa;oBACxC,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;oBAChD,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,IAAI,CAAC,gBAAgB;oBAC7B,YAAY,EAAE,IAAI,CAAC,YAAY;oBAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;oBAC/B,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,QAAQ,EAAE,YAAY,CAAC,QAAQ;AAClC,iBAAA,CAAC,CACL,CAAC;AACL,aAAA;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,kDAAkD,EAClD,aAAa,CAChB,CAAC;AAEF,YAAA,MAAM,IAAI,eAAe,CACrB,6BAA6B,EAC7B,aAAa,CAChB,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAChB,CAA8C,2CAAA,EAAA,KAAK,CAAE,CAAA,EACrD,aAAa,CAChB,CAAC;AAEF,YAAA,OAAO,YAAY,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAC9C,SAAA;KACJ;AAED;;;;AAIG;IACH,MAAM,aAAa,CACf,mBAAwC,EAAA;QAExC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;QAEjE,IAAI;AACA,YAAA,kCAAkC,CAC9B,qBAAqB,EACrB,mBAAmB,EACnB,aAAa,CAChB,CAAC;YAEF,8BAA8B,CAC1B,8BAA8B,EAC9B,mBAAmB,CAAC,QAAQ,EAC5B,aAAa,CAChB,CAAC;AACF,YAAA,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;YAE1C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,EAAE,aAAa,CAAC,CAAC;YAEpE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;AACrD,gBAAA,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ;AAC7C,gBAAA,aAAa,EAAE,aAAa;gBAC5B,aAAa,EACT,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,cAAc,IAAI,EAAE;gBACzD,QAAQ,EAAE,mBAAmB,CAAC,QAAQ;AACzC,aAAA,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,EAAE,aAAa,CAAC,CAAC;AAEnE,YAAA,OAAO,IAAI,wBAAwB,CAC/B,IAAI,8BAA8B,CAAC;gBAC/B,aAAa,EAAE,WAAW,CAAC,aAAa;gBACxC,iBAAiB,EAAE,WAAW,CAAC,iBAAiB;gBAChD,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,gBAAgB;gBAC7B,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;gBAC7C,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,QAAQ,EAAE,mBAAmB,CAAC,QAAQ;gBACtC,UAAU,EAAE,WAAW,CAAC,UAAU;AACrC,aAAA,CAAC,CACL,CAAC;AACL,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAChB,CAAqD,kDAAA,EAAA,KAAK,CAAE,CAAA,EAC5D,aAAa,CAChB,CAAC;AAEF,YAAA,OAAO,wBAAwB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAC1D,SAAA;KACJ;AAEO,IAAA,gBAAgB,CACpB,YAAgD,EAAA;AAEhD,QAAA,QACI,YAAY,EAAE,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,EACnE;KACL;AAEO,IAAA,qBAAqB,CAAC,aAAqB,EAAA;AAC/C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACnC,YAAA,aAAa,EAAE,aAAa;AAC/B,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,aAAa,CAAC,CAAC;AAEhE,YAAA,MAAM,IAAI,wBAAwB,CAAC,aAAa,CAAC,CAAC;AACrD,SAAA;KACJ;AACJ;;;;"}