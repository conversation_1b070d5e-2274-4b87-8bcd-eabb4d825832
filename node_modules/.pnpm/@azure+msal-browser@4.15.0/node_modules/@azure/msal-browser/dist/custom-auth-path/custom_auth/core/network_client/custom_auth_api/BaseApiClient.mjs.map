{"version": 3, "file": "BaseApiClient.mjs", "sources": ["../../../../../../../src/custom_auth/core/network_client/custom_auth_api/BaseApiClient.ts"], "sourcesContent": [null], "names": ["CustomAuthApiErrorCode.HTTP_REQUEST_FAILED", "CustomAuthApiErrorCode.CONTINUATION_TOKEN_MISSING", "CustomAuthApiErrorCode.INVALID_RESPONSE_BODY"], "mappings": ";;;;;;;;AAAA;;;AAGG;MAoBmB,aAAa,CAAA;AAG/B,IAAA,WAAA,CACI,OAAe,EACE,QAAgB,EACzB,UAAuB,EAAA;QADd,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;QACzB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAa;QAE/B,IAAI,CAAC,cAAc,GAAG,QAAQ,CAC1B,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAA,EAAG,OAAO,CAAA,CAAA,CAAG,GAAG,OAAO,CACnD,CAAC;KACL;IAES,MAAM,OAAO,CACnB,QAAgB,EAChB,IAAsC,EACtC,gBAAwC,EACxC,aAAqB,EAAA;AAErB,QAAA,MAAM,QAAQ,GAAG,IAAI,eAAe,CAAC;YACjC,SAAS,EAAE,IAAI,CAAC,QAAQ;AACxB,YAAA,GAAG,IAAI;AACV,SAAA,CAAC,CAAC;QACH,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;AACvE,QAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAEzD,QAAA,IAAI,QAAkB,CAAC;QAEvB,IAAI;AACA,YAAA,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACjE,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,IAAI,kBAAkB,CACxBA,mBAA0C,EAC1C,CAAsB,mBAAA,EAAA,QAAQ,cAAc,CAAC,CAAA,CAAE,EAC/C,aAAa,CAChB,CAAC;AACL,SAAA;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;KAC1D;IAES,8BAA8B,CACpC,iBAAqC,EACrC,aAAqB,EAAA;QAErB,IAAI,CAAC,iBAAiB,EAAE;YACpB,MAAM,IAAI,kBAAkB,CACxBC,0BAAiD,EACjD,oDAAoD,EACpD,aAAa,CAChB,CAAC;AACL,SAAA;KACJ;IAEO,yBAAyB,CAC7B,QAAkB,EAClB,oBAA4B,EAAA;QAE5B,QACI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC;AACpD,YAAA,oBAAoB,EACtB;KACL;IAEO,gBAAgB,CACpB,aAAqB,EACrB,gBAAwC,EAAA;QAExC,OAAO;AACH,YAAA,CAAC,cAAc,CAAC,YAAY,GAAG,mCAAmC;AAClE,YAAA,CAAC,kBAAkB,CAAC,YAAY,GAAG,kBAAkB,CAAC,GAAG;AACzD,YAAA,CAAC,kBAAkB,CAAC,YAAY,GAAG,kBAAkB,CAAC,OAAO;AAC7D,YAAA,CAAC,kBAAkB,CAAC,WAAW,GAAG,kBAAkB,CAAC,EAAE;AACvD,YAAA,CAAC,kBAAkB,CAAC,YAAY,GAAG,kBAAkB,CAAC,GAAG;YACzD,CAAC,kBAAkB,CAAC,mBAAmB,GACnC,gBAAgB,CAAC,iCAAiC,EAAE;YACxD,CAAC,kBAAkB,CAAC,mBAAmB,GACnC,gBAAgB,CAAC,8BAA8B,EAAE;AACrD,YAAA,CAAC,kBAAkB,CAAC,iBAAiB,GAAG,aAAa;SACxD,CAAC;KACL;AAEO,IAAA,MAAM,iBAAiB,CAC3B,QAA8B,EAC9B,oBAA4B,EAAA;QAE5B,IAAI,CAAC,QAAQ,EAAE;YACX,MAAM,IAAI,kBAAkB,CACxB,gBAAgB,EAChB,mBAAmB,EACnB,oBAAoB,CACvB,CAAC;AACL,SAAA;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,yBAAyB,CAChD,QAAQ,EACR,oBAAoB,CACvB,CAAC;AAEF,QAAA,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QAE3C,IAAI,QAAQ,CAAC,EAAE,EAAE;;YAEb,IACI,OAAO,YAAY,KAAK,QAAQ;AAChC,gBAAA,YAAY,CAAC,cAAc,KAAK,aAAa,CAAC,QAAQ,EACxD;AACE,gBAAA,MAAM,IAAI,aAAa,CAAC,aAAa,CAAC,CAAC;AAC1C,aAAA;YAED,OAAO;AACH,gBAAA,GAAG,YAAY;AACf,gBAAA,cAAc,EAAE,aAAa;aAChC,CAAC;AACL,SAAA;QAED,MAAM,aAAa,GAAG,YAAgC,CAAC;QAEvD,IAAI,CAAC,aAAa,EAAE;YAChB,MAAM,IAAI,kBAAkB,CACxBC,qBAA4C,EAC5C,yCAAyC,EACzC,aAAa,CAChB,CAAC;AACL,SAAA;AAED,QAAA,MAAM,UAAU,GACZ,CAAC,CAAC,aAAa,CAAC,mBAAmB;AACnC,YAAA,aAAa,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC;cACtC,aAAa,CAAC,mBAAmB;AACnC,cAAE,aAAa,CAAC,kBAAkB,IAAI,EAAE,CAAC;AAEjD,QAAA,MAAM,IAAI,kBAAkB,CACxB,aAAa,CAAC,KAAK,EACnB,aAAa,CAAC,iBAAiB,EAC/B,aAAa,CAAC,cAAc,EAC5B,aAAa,CAAC,WAAW,EACzB,aAAa,CAAC,QAAQ,EACtB,UAAU,EACV,aAAa,CAAC,kBAAkB,EAChC,aAAa,CAAC,QAAQ,EACtB,aAAa,CAAC,SAAS,CAC1B,CAAC;KACL;AACJ;;;;"}