{"version": 3, "file": "ResetPasswordApiClient.mjs", "sources": ["../../../../../../../src/custom_auth/core/network_client/custom_auth_api/ResetPasswordApiClient.ts"], "sourcesContent": [null], "names": ["CustomAuthApiEndpoint.RESET_PWD_START", "CustomAuthApiEndpoint.RESET_PWD_CHALLENGE", "CustomAuthApiEndpoint.RESET_PWD_CONTINUE", "CustomAuthApiEndpoint.RESET_PWD_SUBMIT", "CustomAuthApiEndpoint.RESET_PWD_POLL", "CustomAuthApiErrorCode.INVALID_POLL_STATUS"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAyBG,MAAO,sBAAuB,SAAQ,aAAa,CAAA;AACrD;;AAEG;IACH,MAAM,KAAK,CACP,MAAiC,EAAA;QAEjC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC7BA,eAAqC,EACrC;YACI,cAAc,EAAE,MAAM,CAAC,cAAc;YACrC,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC5B,EACD,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,aAAa,CACvB,CAAC;QAEF,IAAI,CAAC,8BAA8B,CAC/B,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,aAAa,CACvB,CAAC;AAEF,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;AAGG;IACH,MAAM,gBAAgB,CAClB,MAAqC,EAAA;QAErC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC7BC,mBAAyC,EACzC;YACI,cAAc,EAAE,MAAM,CAAC,cAAc;YACrC,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;SAChD,EACD,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,aAAa,CACvB,CAAC;QAEF,IAAI,CAAC,8BAA8B,CAC/B,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,aAAa,CACvB,CAAC;AAEF,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;AAGG;IACH,MAAM,gBAAgB,CAClB,MAAoC,EAAA;QAEpC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC7BC,kBAAwC,EACxC;YACI,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;YAC7C,UAAU,EAAE,SAAS,CAAC,GAAG;YACzB,GAAG,EAAE,MAAM,CAAC,GAAG;SAClB,EACD,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,aAAa,CACvB,CAAC;QAEF,IAAI,CAAC,8BAA8B,CAC/B,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,aAAa,CACvB,CAAC;AAEF,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;AAGG;IACH,MAAM,iBAAiB,CACnB,MAAkC,EAAA;QAElC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC7BC,gBAAsC,EACtC;YACI,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;YAC7C,YAAY,EAAE,MAAM,CAAC,YAAY;SACpC,EACD,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,aAAa,CACvB,CAAC;QAEF,IAAI,CAAC,8BAA8B,CAC/B,MAAM,CAAC,kBAAkB,EACzB,MAAM,CAAC,aAAa,CACvB,CAAC;AAEF,QAAA,IAAI,MAAM,CAAC,aAAa,KAAK,CAAC,EAAE;AAC5B,YAAA,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC;AAC5B,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB;AAED;;;AAGG;IACH,MAAM,cAAc,CAChB,MAA0C,EAAA;QAE1C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC7BC,cAAoC,EACpC;YACI,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;SAChD,EACD,MAAM,CAAC,gBAAgB,EACvB,MAAM,CAAC,aAAa,CACvB,CAAC;QAEF,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;AAElE,QAAA,OAAO,MAAM,CAAC;KACjB;IAES,uBAAuB,CAC7B,MAAc,EACd,aAAqB,EAAA;AAErB,QAAA,IACI,MAAM,KAAK,uBAAuB,CAAC,MAAM;YACzC,MAAM,KAAK,uBAAuB,CAAC,WAAW;YAC9C,MAAM,KAAK,uBAAuB,CAAC,SAAS;AAC5C,YAAA,MAAM,KAAK,uBAAuB,CAAC,WAAW,EAChD;AACE,YAAA,MAAM,IAAI,kBAAkB,CACxBC,mBAA0C,EAC1C,CAAA,iBAAA,EAAoB,MAAM,CAAA,+BAAA,CAAiC,EAC3D,aAAa,CAChB,CAAC;AACL,SAAA;KACJ;AACJ;;;;"}