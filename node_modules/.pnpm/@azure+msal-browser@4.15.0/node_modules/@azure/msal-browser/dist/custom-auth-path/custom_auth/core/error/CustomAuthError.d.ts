export declare class CustomAuthError extends Error {
    error: string;
    errorDescription?: string | undefined;
    correlationId?: string | undefined;
    errorCodes?: number[] | undefined;
    subError?: string | undefined;
    constructor(error: string, errorDescription?: string | undefined, correlationId?: string | undefined, errorCodes?: number[] | undefined, subError?: string | undefined);
}
//# sourceMappingURL=CustomAuthError.d.ts.map