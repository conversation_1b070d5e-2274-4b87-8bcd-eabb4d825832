import { TokenKeys } from "@azure/msal-common/browser";
import { IWindowStorage } from "./IWindowStorage.js";
/**
 * Returns a list of cache keys for all known accounts
 * @param storage
 * @returns
 */
export declare function getAccountKeys(storage: IWindowStorage<string>): Array<string>;
/**
 * Returns a list of cache keys for all known tokens
 * @param clientId
 * @param storage
 * @returns
 */
export declare function getTokenKeys(clientId: string, storage: IWindowStorage<string>): TokenKeys;
//# sourceMappingURL=CacheHelpers.d.ts.map