/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { CustomAuthError } from './CustomAuthError.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
class MsalCustomAuthError extends CustomAuthError {
    constructor(error, errorDescription, subError, correlationId) {
        super(error, errorDescription, correlationId);
        Object.setPrototypeOf(this, MsalCustomAuthError.prototype);
        this.subError = subError || "";
    }
}

export { MsalCustomAuthError };
//# sourceMappingURL=MsalCustomAuthError.mjs.map
