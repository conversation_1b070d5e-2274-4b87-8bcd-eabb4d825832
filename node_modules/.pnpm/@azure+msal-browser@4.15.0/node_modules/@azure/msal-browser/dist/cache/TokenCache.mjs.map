{"version": 3, "file": "TokenCache.mjs", "sources": ["../../src/cache/TokenCache.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.nonBrowserEnvironment", "BrowserCrypto.createNewGuid", "BrowserAuthErrorCodes.unableToLoadToken"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAsCH;;AAEG;MACU,UAAU,CAAA;AAYnB,IAAA,WAAA,CACI,aAAmC,EACnC,OAA4B,EAC5B,MAAc,EACd,SAAkB,EAAA;AAElB,QAAA,IAAI,CAAC,oBAAoB,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AAC1D,QAAA,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC;AAC5B,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;KAC9B;;AAID;;;;;;AAMG;AACH,IAAA,MAAM,kBAAkB,CACpB,OAAsB,EACtB,QAA+B,EAC/B,OAAyB,EAAA;AAEzB,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC5B,YAAA,MAAM,sBAAsB,CACxBA,qBAA2C,CAC9C,CAAC;AACL,SAAA;QAED,MAAM,aAAa,GACf,OAAO,CAAC,aAAa,IAAIC,aAA2B,EAAE,CAAC;AAE3D,QAAA,MAAM,aAAa,GAAG,QAAQ,CAAC,QAAQ;cACjC,SAAS,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC;cAC7D,SAAS,CAAC;AAEhB,QAAA,MAAM,gBAAgB,GAAqB;AACvC,YAAA,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY;AAC3C,YAAA,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB;AACnD,YAAA,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB;AAC/D,YAAA,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB;AACrD,YAAA,0BAA0B,EACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B;SAClD,CAAC;AACF,QAAA,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS;AAC/B,cAAE,IAAI,SAAS,CACT,SAAS,CAAC,iBAAiB,CACvB,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,iBAAiB,CAC5B,EACD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,EAChC,IAAI,CAAC,OAAO,EACZ,gBAAgB,EAChB,IAAI,CAAC,MAAM,EACX,OAAO,CAAC,aAAa,IAAIA,aAA2B,EAAE,CACzD;cACD,SAAS,CAAC;QAEhB,MAAM,kBAAkB,GAAkB,MAAM,IAAI,CAAC,WAAW,CAC5D,OAAO,EACP,OAAO,CAAC,UAAU,IAAI,QAAQ,CAAC,WAAW,IAAI,EAAE,EAChD,aAAa,EACb,aAAa,EACb,SAAS,CACZ,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAClC,QAAQ,EACR,kBAAkB,CAAC,aAAa,EAChC,kBAAkB,CAAC,WAAW,EAC9B,kBAAkB,CAAC,KAAK,EACxB,aAAa,CAChB,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAC1C,OAAO,EACP,QAAQ,EACR,kBAAkB,CAAC,aAAa,EAChC,kBAAkB,CAAC,WAAW,EAC9B,kBAAkB,CAAC,KAAK,EACxB,OAAO,EACP,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAC5C,QAAQ,EACR,kBAAkB,CAAC,aAAa,EAChC,kBAAkB,CAAC,WAAW,EAC9B,aAAa,CAChB,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC,4BAA4B,CACpC,OAAO,EACP;AACI,YAAA,OAAO,EAAE,kBAAkB;YAC3B,OAAO;YACP,WAAW;YACX,YAAY;AACf,SAAA,EACD,aAAa,EACb,SAAS,CACZ,CAAC;KACL;AAED;;;;;;;;AAQG;IACK,MAAM,WAAW,CACrB,OAAsB,EACtB,UAAkB,EAClB,aAAqB,EACrB,aAA2B,EAC3B,SAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;QAEpD,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,MAAM,aAAa,GAAG,aAAa,CAAC,qBAAqB,CACrD,OAAO,CAAC,OAAO,CAClB,CAAC;YACF,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAC5D,YAAA,OAAO,aAAa,CAAC;AACxB,SAAA;aAAM,IAAI,CAAC,SAAS,KAAK,CAAC,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE;AACtD,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,iIAAiI,CACpI,CAAC;AACF,YAAA,MAAM,sBAAsB,CACxBC,iBAAuC,CAC1C,CAAC;AACL,SAAA;QAED,MAAM,aAAa,GAAG,aAAa,CAAC,qBAAqB,CACrD,UAAU,EACV,SAAS,CAAC,aAAa,EACvB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,SAAS,EACd,aAAa,CAChB,CAAC;AAEF,QAAA,MAAM,cAAc,GAAG,aAAa,EAAE,GAAG,CAAC;AAE1C,QAAA,MAAM,aAAa,GAAG,mBAAmB,CACrC,IAAI,CAAC,OAAO,EACZ,SAAS,EACT,aAAa,EACb,YAAY,EACZ,aAAa,EACb,aAAa,EACb,UAAU,EACV,SAAS,CAAC,eAAe,EACzB,cAAc,EACd,SAAS;AACT,QAAA,SAAS;QACT,IAAI,CAAC,MAAM,CACd,CAAC;QAEF,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAC5D,QAAA,OAAO,aAAa,CAAC;KACxB;AAED;;;;;;;AAOG;IACK,MAAM,WAAW,CACrB,QAA+B,EAC/B,aAAqB,EACrB,WAAmB,EACnB,QAAgB,EAChB,aAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AACpB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;AAClE,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;QACrD,MAAM,aAAa,GAAG,YAAY,CAAC,mBAAmB,CAClD,aAAa,EACb,WAAW,EACX,QAAQ,CAAC,QAAQ,EACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACzB,QAAQ,CACX,CAAC;QAEF,MAAM,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AACtE,QAAA,OAAO,aAAa,CAAC;KACxB;AAED;;;;;;;;AAQG;AACK,IAAA,MAAM,eAAe,CACzB,OAAsB,EACtB,QAA+B,EAC/B,aAAqB,EACrB,WAAmB,EACnB,QAAgB,EAChB,OAAyB,EACzB,aAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;AACxB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,gDAAgD,CACnD,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAAM,aAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;AAC7B,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,iFAAiF,CACpF,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;aAAM,IACH,CAAC,QAAQ,CAAC,KAAK;AACf,aAAC,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAC7C;AACE,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,8FAA8F,CACjG,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC;AAEzD,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK;cACvB,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;cACnC,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,QAAA,MAAM,SAAS,GACX,OAAO,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AAEtE,QAAA,MAAM,iBAAiB,GACnB,OAAO,CAAC,iBAAiB;AACzB,YAAA,CAAC,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,UAAU;gBAC3C,SAAS,CAAC,UAAU,EAAE,CAAC;AAE/B,QAAA,MAAM,iBAAiB,GAAG,YAAY,CAAC,uBAAuB,CAC1D,aAAa,EACb,WAAW,EACX,QAAQ,CAAC,YAAY,EACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACzB,QAAQ,EACR,MAAM,CAAC,WAAW,EAAE,EACpB,SAAS,EACT,iBAAiB,EACjB,YAAY,CACf,CAAC;QAEF,MAAM,IAAI,CAAC,OAAO,CAAC,wBAAwB,CACvC,iBAAiB,EACjB,aAAa,CAChB,CAAC;AACF,QAAA,OAAO,iBAAiB,CAAC;KAC5B;AAED;;;;;;;AAOG;IACK,MAAM,gBAAgB,CAC1B,QAA+B,EAC/B,aAAqB,EACrB,WAAmB,EACnB,aAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;AACzB,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,iDAAiD,CACpD,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oCAAoC,CAAC,CAAC;AAC1D,QAAA,MAAM,kBAAkB,GAAG,YAAY,CAAC,wBAAwB,CAC5D,aAAa,EACb,WAAW,EACX,QAAQ,CAAC,aAAa,EACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACzB,QAAQ,CAAC,IAAI,EACb,SAAS;QACT,QAAQ,CAAC,wBAAwB,CACpC,CAAC;QAEF,MAAM,IAAI,CAAC,OAAO,CAAC,yBAAyB,CACxC,kBAAkB,EAClB,aAAa,CAChB,CAAC;AACF,QAAA,OAAO,kBAAkB,CAAC;KAC7B;AAED;;;;;;;AAOG;AACK,IAAA,4BAA4B,CAChC,OAAsB,EACtB,WAAqD,EACrD,aAA2B,EAC3B,SAAqB,EAAA;QAErB,IAAI,WAAW,GAAW,EAAE,CAAC;QAC7B,IAAI,cAAc,GAAkB,EAAE,CAAC;QACvC,IAAI,SAAS,GAAgB,IAAI,CAAC;AAClC,QAAA,IAAI,YAA8B,CAAC;QAEnC,IAAI,WAAW,EAAE,WAAW,EAAE;AAC1B,YAAA,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC;AAC7C,YAAA,cAAc,GAAG,QAAQ,CAAC,UAAU,CAChC,WAAW,CAAC,WAAW,CAAC,MAAM,CACjC,CAAC,OAAO,EAAE,CAAC;;YAEZ,SAAS,GAAG,SAAS,CAAC,iBAAiB,CACnC,WAAW,CAAC,WAAW,CAAC,SAAS,CACpC,CAAC;YACF,YAAY,GAAG,SAAS,CAAC,iBAAiB,CACtC,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAC5C,CAAC;AACL,SAAA;AAED,QAAA,MAAM,aAAa,GAAG,WAAW,CAAC,OAAO,CAAC;QAE1C,OAAO;YACH,SAAS,EAAE,SAAS,GAAG,SAAS,CAAC,kBAAkB,GAAG,EAAE;AACxD,YAAA,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,cAAc;AAC5C,YAAA,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK;AACnC,YAAA,MAAM,EAAE,cAAc;AACtB,YAAA,OAAO,EAAE,aAAa,CAAC,cAAc,EAAE;AACvC,YAAA,OAAO,EAAE,WAAW,CAAC,OAAO,EAAE,MAAM,IAAI,EAAE;YAC1C,aAAa,EAAE,aAAa,IAAI,EAAE;AAClC,YAAA,WAAW,EAAE,WAAW;AACxB,YAAA,SAAS,EAAE,IAAI;AACf,YAAA,SAAS,EAAE,SAAS;AACpB,YAAA,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,EAAE;AAC1C,YAAA,SAAS,EAAE,EAAE;AACb,YAAA,YAAY,EAAE,YAAY;AAC1B,YAAA,QAAQ,EAAE,WAAW,CAAC,YAAY,EAAE,QAAQ,IAAI,EAAE;AAClD,YAAA,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,IAAI,EAAE;AACpD,YAAA,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE;AAC1B,YAAA,kBAAkB,EAAE,aAAa,CAAC,kBAAkB,IAAI,EAAE;AAC1D,YAAA,WAAW,EAAE,aAAa,CAAC,WAAW,IAAI,EAAE;AAC5C,YAAA,gBAAgB,EAAE,KAAK;SAC1B,CAAC;KACL;AACJ;;;;"}