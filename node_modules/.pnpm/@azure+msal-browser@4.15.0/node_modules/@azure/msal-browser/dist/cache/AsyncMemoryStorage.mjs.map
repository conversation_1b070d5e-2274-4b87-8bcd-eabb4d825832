{"version": 3, "file": "AsyncMemoryStorage.mjs", "sources": ["../../src/cache/AsyncMemoryStorage.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.databaseUnavailable"], "mappings": ";;;;;;;AAAA;;;AAGG;AAWH;;;AAGG;MACU,kBAAkB,CAAA;AAK3B,IAAA,WAAA,CAAY,MAAc,EAAA;AACtB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,EAAK,CAAC;AAC5C,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI,eAAe,EAAK,CAAC;AAC/C,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;KACxB;AAEO,IAAA,yBAAyB,CAAC,KAAc,EAAA;QAC5C,IACI,KAAK,YAAY,gBAAgB;AACjC,YAAA,KAAK,CAAC,SAAS,KAAKA,mBAAyC,EAC/D;AACE,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,6IAA6I,CAChJ,CAAC;AACL,SAAA;AAAM,aAAA;AACH,YAAA,MAAM,KAAK,CAAC;AACf,SAAA;KACJ;AACD;;;;AAIG;IACH,MAAM,OAAO,CAAC,GAAW,EAAA;QACrB,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,EAAE;YACP,IAAI;AACA,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,6EAA6E,CAChF,CAAC;gBACF,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACjD,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;AACrC,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf;AAED;;;;;AAKG;AACH,IAAA,MAAM,OAAO,CAAC,GAAW,EAAE,KAAQ,EAAA;QAC/B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACvC,IAAI;YACA,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACjD,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;AACrC,SAAA;KACJ;AAED;;;AAGG;IACH,MAAM,UAAU,CAAC,GAAW,EAAA;AACxB,QAAA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI;YACA,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC7C,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;AACrC,SAAA;KACJ;AAED;;;AAGG;AACH,IAAA,MAAM,OAAO,GAAA;QACT,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AAC/C,QAAA,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,IAAI;AACA,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4DAA4D,CAC/D,CAAC;AACF,gBAAA,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;AAC9C,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;AACrC,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,SAAS,CAAC;KACpB;AAED;;;AAGG;IACH,MAAM,WAAW,CAAC,GAAW,EAAA;QACzB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACxD,IAAI,CAAC,WAAW,EAAE;YACd,IAAI;AACA,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,oEAAoE,CACvE,CAAC;gBACF,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACrD,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACR,gBAAA,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;AACrC,aAAA;AACJ,SAAA;AACD,QAAA,OAAO,WAAW,CAAC;KACtB;AAED;;AAEG;IACH,aAAa,GAAA;;AAET,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA,2BAAA,CAA6B,CAAC,CAAC;AACnD,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA,0BAAA,CAA4B,CAAC,CAAC;KACrD;AAED;;;AAGG;AACH,IAAA,MAAM,eAAe,GAAA;QACjB,IAAI;AACA,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YACpD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;AAC7D,YAAA,IAAI,SAAS,EAAE;AACX,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;AACtD,aAAA;AAED,YAAA,OAAO,SAAS,CAAC;AACpB,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;AAClC,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;KACJ;AACJ;;;;"}