{"version": 3, "file": "LocalStorage.mjs", "sources": ["../../src/cache/LocalStorage.ts"], "sourcesContent": [null], "names": ["BrowserConfigurationAuthErrorCodes.storageNotSupported", "BrowserAuthErrorCodes.uninitializedPublicClientApplication"], "mappings": ";;;;;;;;;;;;;;;AAAA;;;AAGG;AAkCH,MAAM,cAAc,GAAG,uBAAuB,CAAC;AAC/C,MAAM,sBAAsB,GAAG,sBAAsB,CAAC;MAczC,YAAY,CAAA;AASrB,IAAA,WAAA,CACI,QAAgB,EAChB,MAAc,EACd,iBAAqC,EAAA;AAErC,QAAA,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;AACtB,YAAA,MAAM,mCAAmC,CACrCA,mBAAsD,CACzD,CAAC;AACL,SAAA;AACD,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,EAAU,CAAC;AACjD,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,gBAAgB,CAAC,sBAAsB,CAAC,CAAC;KACjE;IAED,MAAM,UAAU,CAAC,aAAqB,EAAA;AAClC,QAAA,MAAM,OAAO,GAAG,IAAI,aAAa,EAAE,CAAC;QACpC,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,YAAY,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvC,QAAA,IAAI,YAAY,EAAE;YACd,IAAI;AACA,gBAAA,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAC3C,aAAA;YAAC,OAAO,CAAC,EAAE,GAAE;AACjB,SAAA;AACD,QAAA,IAAI,YAAY,CAAC,GAAG,IAAI,YAAY,CAAC,EAAE,EAAE;;YAErC,MAAM,OAAO,GAAG,MAAM,CAClB,cAAc,EACd,iBAAiB,CAAC,YAAY,EAC9B,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACpB,IAAI,CAAC,gBAAgB,GAAG;gBACpB,EAAE,EAAE,YAAY,CAAC,EAAE;gBACnB,GAAG,EAAE,MAAM,WAAW,CAClB,YAAY,EACZ,iBAAiB,CAAC,YAAY,EAC9B,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,OAAO,CAAC;aACb,CAAC;AACF,YAAA,MAAM,WAAW,CACb,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EACnC,iBAAiB,CAAC,mBAAmB,EACrC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,aAAa,CAAC,CAAC;AACpB,SAAA;AAAM,aAAA;;YAEH,IAAI,CAAC,KAAK,EAAE,CAAC;AACb,YAAA,MAAM,EAAE,GAAG,aAAa,EAAE,CAAC;YAC3B,MAAM,OAAO,GAAG,MAAM,WAAW,CAC7B,eAAe,EACf,iBAAiB,CAAC,eAAe,EACjC,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,EAAE,CAAC;YACJ,MAAM,MAAM,GAAG,MAAM,CACjB,YAAY,EACZ,iBAAiB,CAAC,YAAY,EAC9B,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG;AACpB,gBAAA,EAAE,EAAE,EAAE;gBACN,GAAG,EAAE,MAAM,WAAW,CAClB,YAAY,EACZ,iBAAiB,CAAC,YAAY,EAC9B,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,OAAO,CAAC;aACb,CAAC;AAEF,YAAA,MAAM,UAAU,GAAG;AACf,gBAAA,EAAE,EAAE,EAAE;AACN,gBAAA,GAAG,EAAE,MAAM;aACd,CAAC;AAEF,YAAA,OAAO,CAAC,OAAO,CACX,cAAc,EACd,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAC1B,CAAC;AACD,YAAA,IAAI;YACJ,eAAe,CAAC,IAAI;aACvB,CAAC;AACL,SAAA;;AAGD,QAAA,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAExE,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KAC3B;AAED,IAAA,OAAO,CAAC,GAAW,EAAA;QACf,OAAO,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KAC3C;AAED,IAAA,WAAW,CAAC,GAAW,EAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACnB,YAAA,MAAM,sBAAsB,CACxBC,oCAA0D,CAC7D,CAAC;AACL,SAAA;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KAC1C;IAED,OAAO,CAAC,GAAW,EAAE,KAAa,EAAA;QAC9B,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KAC3C;IAED,MAAM,WAAW,CACb,GAAW,EACX,KAAa,EACb,aAAqB,EACrB,SAAiB,EAAA;QAEjB,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC7C,YAAA,MAAM,sBAAsB,CACxBA,oCAA0D,CAC7D,CAAC;AACL,SAAA;AAED,QAAA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,WAAW,CACrC,OAAO,EACP,iBAAiB,CAAC,OAAO,EACzB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1D,QAAA,MAAM,aAAa,GAAkB;AACjC,YAAA,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE;AAC5B,YAAA,KAAK,EAAE,KAAK;AACZ,YAAA,IAAI,EAAE,IAAI;AACV,YAAA,aAAa,EAAE,SAAS;SAC3B,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC;;AAGjD,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;AACvB,YAAA,GAAG,EAAE,GAAG;AACR,YAAA,KAAK,EAAE,KAAK;AACZ,YAAA,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;AAChC,SAAA,CAAC,CAAC;KACN;AAED,IAAA,UAAU,CAAC,GAAW,EAAA;QAClB,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;AACrC,YAAA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC,YAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;AACvB,gBAAA,GAAG,EAAE,GAAG;AACR,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;AAChC,aAAA,CAAC,CAAC;AACN,SAAA;AACD,QAAA,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KACvC;IAED,OAAO,GAAA;QACH,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KAC3C;AAED,IAAA,WAAW,CAAC,GAAW,EAAA;QACnB,OAAO,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;KAClD;AAED;;AAEG;IACH,KAAK,GAAA;;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AAE3B,QAAA,MAAM,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;AACzC,QAAA,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QACnD,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACpD,QAAA,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,QAAA,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7D,QAAA,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;;QAG9D,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,QAAgB,KAAI;AACxC,YAAA,IACI,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,YAAY,CAAC;gBAC3C,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EACxC;AACE,gBAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC7B,aAAA;AACL,SAAC,CAAC,CAAC;KACN;AAED;;;AAGG;IACK,MAAM,mBAAmB,CAAC,aAAqB,EAAA;AACnD,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO;AACV,SAAA;AAED,QAAA,IAAI,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;QACvC,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;;AAEjE,QAAA,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;QAExE,MAAM,SAAS,GAAc,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC/D,QAAA,SAAS,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CACtC,SAAS,CAAC,OAAO,EACjB,aAAa,CAChB,CAAC;AACF,QAAA,SAAS,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,CAC1C,SAAS,CAAC,WAAW,EACrB,aAAa,CAChB,CAAC;AACF,QAAA,SAAS,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,WAAW,CAC3C,SAAS,CAAC,YAAY,EACtB,aAAa,CAChB,CAAC;;QAEF,IAAI,CAAC,OAAO,CACR,CAAA,EAAG,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAA,EAChD,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAC5B,CAAC;KACL;AAED;;;;AAIG;AACK,IAAA,MAAM,yBAAyB,CACnC,GAAW,EACX,aAAqB,EAAA;AAErB,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AACxB,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,QAAQ,EAAE;AACX,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,MAAqB,CAAC;QAC1B,IAAI;AACA,YAAA,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACjC,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;;AAER,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;;AAE7C,YAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAClC,EAAE,qBAAqB,EAAE,CAAC,EAAE,EAC5B,aAAa,CAChB,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE;;AAExC,YAAA,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAClC,EAAE,0BAA0B,EAAE,CAAC,EAAE,EACjC,aAAa,CAChB,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,OAAO,WAAW,CACd,OAAO,EACP,iBAAiB,CAAC,OAAO,EACzB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,iBAAiB,EACtB,aAAa,CAChB,CACG,IAAI,CAAC,gBAAgB,CAAC,GAAG,EACzB,MAAM,CAAC,KAAK,EACZ,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EACpB,MAAM,CAAC,IAAI,CACd,CAAC;KACL;AAED;;;;AAIG;AACK,IAAA,MAAM,WAAW,CACrB,GAAkB,EAClB,aAAqB,EAAA;QAErB,MAAM,WAAW,GAAkB,EAAE,CAAC;QACtC,MAAM,UAAU,GAAyB,EAAE,CAAC;AAC5C,QAAA,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AAChB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,yBAAyB,CAC1C,GAAG,EACH,aAAa,CAChB,CAAC,IAAI,CAAC,CAAC,KAAK,KAAI;AACb,gBAAA,IAAI,KAAK,EAAE;oBACP,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,oBAAA,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,iBAAA;AAAM,qBAAA;;AAEH,oBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACxB,iBAAA;AACL,aAAC,CAAC,CAAC;AACH,YAAA,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7B,SAAC,CAAC,CAAC;AAEH,QAAA,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAC9B,QAAA,OAAO,WAAW,CAAC;KACtB;AAED;;;;AAIG;AACK,IAAA,UAAU,CAAC,GAAW,EAAA;QAC1B,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC7B,YAAA,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC3B,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAClB;AAEO,IAAA,WAAW,CAAC,KAAmB,EAAA;AACnC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;AAClE,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAC3D,iBAAiB,CAAC,mBAAmB,CACxC,CAAC;QACF,eAAe,CAAC,GAAG,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;QAE5C,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;QAC3C,IAAI,CAAC,GAAG,EAAE;AACN,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;AACjD,YAAA,eAAe,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;YAC5D,OAAO;AACV,SAAA;AAED,QAAA,IAAI,OAAO,IAAI,OAAO,KAAK,IAAI,CAAC,QAAQ,EAAE;YACtC,IAAI,CAAC,MAAM,CAAC,KAAK,CACb,CAA2C,wCAAA,EAAA,OAAO,CAAE,CAAA,CACvD,CAAC;YACF,eAAe,CAAC,GAAG,CAAC;AAChB,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,SAAS,EAAE,iBAAiB;AAC/B,aAAA,CAAC,CAAC;YACH,OAAO;AACV,SAAA;QAED,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC;AAC3D,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACvC,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACzD,SAAA;QACD,eAAe,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;KAC1C;AACJ;;;;"}