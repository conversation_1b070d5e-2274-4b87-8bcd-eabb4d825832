{"version": 3, "file": "DatabaseStorage.mjs", "sources": ["../../src/cache/DatabaseStorage.ts"], "sourcesContent": [null], "names": ["BrowserAuthErrorCodes.databaseUnavailable", "BrowserAuthErrorCodes.databaseNotOpen"], "mappings": ";;;;;;AAAA;;;AAGG;AAyBH;;AAEG;MACU,eAAe,CAAA;AAOxB,IAAA,WAAA,GAAA;AACI,QAAA,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;AACtB,QAAA,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC;AAC/B,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;KACvB;AAED;;AAEG;AACH,IAAA,MAAM,IAAI,GAAA;QACN,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACnC,YAAA,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAChE,MAAM,CAAC,gBAAgB,CACnB,eAAe,EACf,CAAC,CAAwB,KAAI;gBACzB,MAAM,KAAK,GAAG,CAAgC,CAAC;gBAC/C,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1D,aAAC,CACJ,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAQ,KAAI;gBAC5C,MAAM,KAAK,GAAG,CAA0B,CAAC;gBACzC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;AAC9B,gBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,gBAAA,OAAO,EAAE,CAAC;AACd,aAAC,CAAC,CAAC;AACH,YAAA,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAC7B,MAAM,CACF,sBAAsB,CAClBA,mBAAyC,CAC5C,CACJ,CACJ,CAAC;AACN,SAAC,CAAC,CAAC;KACN;AAED;;;AAGG;IACH,eAAe,GAAA;AACX,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;AACnB,QAAA,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;YACnB,EAAE,CAAC,KAAK,EAAE,CAAC;AACX,YAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACvB,SAAA;KACJ;AAED;;AAEG;AACK,IAAA,MAAM,gBAAgB,GAAA;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACd,YAAA,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACtB,SAAA;KACJ;AAED;;;AAGG;IACH,MAAM,OAAO,CAAC,GAAW,EAAA;AACrB,QAAA,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,IAAI,OAAO,CAAI,CAAC,OAAO,EAAE,MAAM,KAAI;;AAEtC,YAAA,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;gBACV,OAAO,MAAM,CACT,sBAAsB,CAClBC,eAAqC,CACxC,CACJ,CAAC;AACL,aAAA;AACD,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CACnC,CAAC,IAAI,CAAC,SAAS,CAAC,EAChB,UAAU,CACb,CAAC;YACF,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5D,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAEnC,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAQ,KAAI;gBAC3C,MAAM,KAAK,GAAG,CAAoB,CAAC;gBACnC,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,gBAAA,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,aAAC,CAAC,CAAC;YAEH,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAQ,KAAI;gBACzC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACN;AAED;;;;AAIG;AACH,IAAA,MAAM,OAAO,CAAC,GAAW,EAAE,OAAU,EAAA;AACjC,QAAA,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,IAAI,OAAO,CAAO,CAAC,OAAiB,EAAE,MAAgB,KAAI;;AAE7D,YAAA,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;gBACV,OAAO,MAAM,CACT,sBAAsB,CAClBA,eAAqC,CACxC,CACJ,CAAC;AACL,aAAA;AACD,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CACnC,CAAC,IAAI,CAAC,SAAS,CAAC,EAChB,WAAW,CACd,CAAC;YAEF,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE5D,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAE5C,YAAA,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAK;gBACnC,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,gBAAA,OAAO,EAAE,CAAC;AACd,aAAC,CAAC,CAAC;YAEH,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,KAAI;gBAClC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACN;AAED;;;AAGG;IACH,MAAM,UAAU,CAAC,GAAW,EAAA;AACxB,QAAA,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,IAAI,OAAO,CAAO,CAAC,OAAiB,EAAE,MAAgB,KAAI;AAC7D,YAAA,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;gBACV,OAAO,MAAM,CACT,sBAAsB,CAClBA,eAAqC,CACxC,CACJ,CAAC;AACL,aAAA;AAED,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CACnC,CAAC,IAAI,CAAC,SAAS,CAAC,EAChB,WAAW,CACd,CAAC;YACF,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5D,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAEzC,YAAA,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAK;gBACtC,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,gBAAA,OAAO,EAAE,CAAC;AACd,aAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,KAAI;gBACrC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACN;AAED;;AAEG;AACH,IAAA,MAAM,OAAO,GAAA;AACT,QAAA,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,OAAO,IAAI,OAAO,CAAW,CAAC,OAAiB,EAAE,MAAgB,KAAI;AACjE,YAAA,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;gBACV,OAAO,MAAM,CACT,sBAAsB,CAClBA,eAAqC,CACxC,CACJ,CAAC;AACL,aAAA;AAED,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CACnC,CAAC,IAAI,CAAC,SAAS,CAAC,EAChB,UAAU,CACb,CAAC;YACF,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC5D,YAAA,MAAM,SAAS,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC;YAE3C,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAQ,KAAI;gBAC/C,MAAM,KAAK,GAAG,CAAoB,CAAC;gBACnC,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,gBAAA,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,aAAC,CAAC,CAAC;YAEH,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAQ,KAAI;gBAC7C,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACN;AAED;;;AAGG;IACH,MAAM,WAAW,CAAC,GAAW,EAAA;AACzB,QAAA,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE9B,OAAO,IAAI,OAAO,CAAU,CAAC,OAAiB,EAAE,MAAgB,KAAI;AAChE,YAAA,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;gBACV,OAAO,MAAM,CACT,sBAAsB,CAClBA,eAAqC,CACxC,CACJ,CAAC;AACL,aAAA;AAED,YAAA,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CACnC,CAAC,IAAI,CAAC,SAAS,CAAC,EAChB,UAAU,CACb,CAAC;YACF,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5D,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAE7C,aAAa,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAQ,KAAI;gBACnD,MAAM,KAAK,GAAG,CAAoB,CAAC;gBACnC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;AACvC,aAAC,CAAC,CAAC;YAEH,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAQ,KAAI;gBACjD,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM,CAAC,CAAC,CAAC,CAAC;AACd,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACN;AAED;;;;AAIG;AACH,IAAA,MAAM,cAAc,GAAA;;AAGhB,QAAA,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;YACxB,IAAI,CAAC,eAAe,EAAE,CAAC;AAC1B,SAAA;QAED,OAAO,IAAI,OAAO,CAAU,CAAC,OAAiB,EAAE,MAAgB,KAAI;YAChE,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACjE,YAAA,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AAChD,YAAA,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAK;gBAC7C,YAAY,CAAC,EAAE,CAAC,CAAC;AACjB,gBAAA,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;AACzB,aAAC,CAAC,CAAC;AACH,YAAA,eAAe,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAK;gBAC7C,YAAY,CAAC,EAAE,CAAC,CAAC;AACjB,gBAAA,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;AACzB,aAAC,CAAC,CAAC;AACH,YAAA,eAAe,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAK;gBAC3C,YAAY,CAAC,EAAE,CAAC,CAAC;AACjB,gBAAA,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACzB,aAAC,CAAC,CAAC;AACP,SAAC,CAAC,CAAC;KACN;AACJ;;;;"}