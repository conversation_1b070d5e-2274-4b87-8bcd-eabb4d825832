"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errtion = void 0;
/**
 * Allow code and level inputs on Errlop.
 * We do this instead of a class extension, as class extensions do not interop well on node 0.8, which is our target.
 */
function errtion(opts, parent) {
    // extract opts
    var code = opts.code, level = opts.level;
    var message = opts.message;
    if (parent == null)
        parent = opts.parent;
    // append message
    if (code)
        message = "".concat(code, ": ").concat(message);
    if (level)
        message = "".concat(level, ": ").concat(message);
    if (parent)
        message = "".concat(message, "\n\u21AA").concat(parent.message || parent);
    // create error
    var error = new Error(message);
    // add properties
    if (code)
        error.code = code;
    if (level)
        error.level = level;
    if (parent)
        error.parent = parent;
    // return
    return error;
}
exports.errtion = errtion;
