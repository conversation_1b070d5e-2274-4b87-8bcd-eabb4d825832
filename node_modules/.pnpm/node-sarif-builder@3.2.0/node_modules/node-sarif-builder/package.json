{"name": "node-sarif-builder", "version": "3.2.0", "description": "Module to help building SARIF log files", "main": "dist/index.js", "typings": "dist/index.d.ts", "module": "dist/index.js", "repository": "https://github.com/nvuillam/node-sarif-builder", "license": "MIT", "keywords": ["sarif", "builder", "node", "simple", "linter", "output", "format"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://nicolas.vuillamy.fr"}, "scripts": {"fix": "run-s fix:*", "fix:prettier": "prettier \"src/**/*.ts\" --write", "fix:lint": "eslint src --ext .ts --fix", "test": "yarn test:unit", "test:lint": "eslint src --ext .ts", "test:prettier": "prettier \"src/**/*.ts\" --list-different", "test:unit": "nyc --silent ava", "pretest": "tsc", "diff-integration-tests": "mkdir -p diff && rm -rf diff/test && cp -r test diff/test && rm -rf diff/test/test-*/.git && cd diff && git init --quiet && git add -A && git commit --quiet --no-verify --allow-empty -m 'WIP' && echo '\\n\\nCommitted most recent integration test output in the \"diff\" directory. Review the changes with \"cd diff && git diff HEAD\" or your preferred git diff viewer.'", "watch:build": "tsc -p tsconfig.json -w", "watch:test": "nyc --silent ava --watch", "cov": "run-s build test:unit cov:html cov:lcov && open-cli coverage/index.html", "cov:html": "nyc report --reporter=html", "cov:lcov": "nyc report --reporter=lcov", "cov:send": "run-s cov:lcov && codecov", "cov:check": "nyc report && nyc check-coverage --lines 100 --functions 100 --branches 100", "prepublish": "tsc"}, "engines": {"node": ">=18"}, "files": ["dist", "dist/index.js", "!**/*.spec.*", "!**/*.json", "CHANGELOG.md", "LICENSE", "README.md"], "dependencies": {"@types/sarif": "^2.1.7", "fs-extra": "^11.1.1"}, "devDependencies": {"@ava/typescript": "^5.0.0", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/fs-extra": "^11.0.4", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "ava": "^6.0.0", "codecov": "^3.8.2", "cspell": "^8.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.29.0", "nyc": "^17.0.0", "prettier": "^3.1.0", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "ava": {"failFast": true, "timeout": "60s", "typescript": {"rewritePaths": {"src/": "dist/"}, "compile": false}, "files": ["!build/module/**"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "prettier": {"singleQuote": true}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript", "exclude": ["**/*.spec.js"]}}