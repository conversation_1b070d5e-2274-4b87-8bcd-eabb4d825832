---
#
# Documentation:
# https://help.github.com/en/articles/workflow-syntax-for-github-actions
#

#######################################
# Start the job on all push to master #
#######################################
name: 'Build & Deploy - RELEASE'
on:
  release:
    # Want to run the automation when a release is created
    types: ['created']

permissions: read-all

###############
# Set the Job #
###############
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      # Setup .npmrc file to publish to npm
      - uses: actions/setup-node@v4
        with:
          node-version: '18.x'
          registry-url: 'https://registry.npmjs.org'
          # Defaults to the user or organization that owns the workflow file
          scope: 'nvuillam'
      - run: yarn
      - run: yarn publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
