---
#
# Documentation:
# https://help.github.com/en/articles/workflow-syntax-for-github-actions
#

#######################################
# Start the job on all push to master #
#######################################
name: 'Build & Deploy - Beta'
on:
  push:
    branches:
      - 'main'

permissions: read-all

###############
# Set the Job #
###############
jobs:
  deploy:
    name: Deploy beta
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      # Setup .npmrc file to publish to npm
      - uses: actions/setup-node@v4
        with:
          node-version: '18.x'
          registry-url: 'https://registry.npmjs.org'
          always-auth: true
          # Defaults to the user or organization that owns the workflow file
          scope: 'nvuillam'
      - run: yarn
      - run: yarn config set version-git-tag false
      - run: BETAID=$(date '+%Y%m%d%H%M') && yarn version --prepatch --preid="beta$BETAID"
      - run: yarn publish --tag beta
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
