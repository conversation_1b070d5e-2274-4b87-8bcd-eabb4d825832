# Configuration file for <PERSON><PERSON>inter
# See all available variables at https://megalinter.io/latest/configuration/ and in linters documentation

APPLY_FIXES: none # all, none, or list of linter keys
# ENABLE: # If you use <PERSON><PERSON><PERSON><PERSON> variable, all other languages/formats/tooling-formats will be disabled by default
# ENABLE_LINTERS: # If you use ENABLE_LINTERS variable, all other linters will be disabled by default
# DISABLE:
# - COPYPASTE # Uncomment to disable checks of excessive copy-pastes
# - SPELL # Uncomment to disable checks of spelling mistakes
SHOW_ELAPSED_TIME: true
FILEIO_REPORTER: false
DISABLE_ERRORS_LINTERS:
  - ACTION_ACTIONLINT
  - REPOSITORY_TRUFFLEHOG
  - SPELL_LYCHEE
SPELL_FILTER_REGEX_EXCLUDE: (github-dependents-info)
DISABLE_LINTERS:
  - MARKDOWN_MARKDOWN_LINK_CHECK
  - TYPESCRIPT_STANDARD
PRE_COMMANDS:
  - command: yarn install --frozen-lockfile
    cwd: workspace
  - command: yarn add eslint-plugin-eslint-comments
    cwd: workspace
JSON_JSONLINT_FILTER_REGEX_EXCLUDE: (\.vscode|tsconfig\.json)
MARKDOWN_MARKDOWNLINT_FILTER_REGEX_EXCLUDE: (\\.github)
REPOSITORY_TRIVY_ARGUMENTS:
  - --skip-dirs
  - ./node_modules/uri-js
TYPESCRIPT_DEFAULT_STYLE: prettier
