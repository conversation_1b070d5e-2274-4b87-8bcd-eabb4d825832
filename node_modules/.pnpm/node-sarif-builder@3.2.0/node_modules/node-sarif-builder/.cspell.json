{"ignorePaths": ["**/node_modules/**", "**/vscode-extension/**", "**/.git/**", ".vscode", "megalinter", "package-lock.json", "report"], "language": "en", "version": "0.1", "words": ["ABAP", "ALPHAID", "AMPL", "ANTLR", "Agda", "Augeas", "BETAID", "Batchfile", "<PERSON><PERSON><PERSON>", "Bluespec", "Brightscript", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Cirru", "<PERSON><PERSON>", "Cycript", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Dogescript", "Ecere", "Eclass", "GLSL", "<PERSON><PERSON>", "Gettext", "<PERSON><PERSON>", "<PERSON><PERSON>", "Graphviz", "<PERSON><PERSON><PERSON>", "Guids", "HLSL", "<PERSON><PERSON>", "Haxe", "<PERSON><PERSON><PERSON>", "Inn<PERSON>", "<PERSON><PERSON>", "JSONLD", "JSONLINT", "Jetbrains", "LOLCODE", "<PERSON><PERSON>", "Logtalk", "MARKDOWNLINT", "MTML", "Mathematica", "Mirah", "Modelica", "Moocode", "Multiformat", "<PERSON><PERSON>", "NSIS", "<PERSON><PERSON><PERSON><PERSON>", "Nooo", "Omgrofl", "Oxygene", "PLSQL", "Pico", "RAML", "RHTML", "<PERSON><PERSON>", "Rebol", "Redcode", "<PERSON>'<PERSON>y", "SARIF", "SAST", "SPARQL", "SQLPL", "STON", "<PERSON><PERSON><PERSON>", "Scaml", "<PERSON><PERSON><PERSON>", "<PERSON>", "Smali", "Stata", "Tcsh", "TRUFFLEHOG", "VHDL", "Vala", "Verilog", "<PERSON><PERSON><PERSON><PERSON>", "Xojo", "<PERSON><PERSON><PERSON>", "Zimpl", "abap", "adoc", "agda", "ahkl", "ampl", "apacheconf", "applescript", "arpa", "asax", "ashx", "asmx", "badgemarkdownfile", "befunge", "capnp", "ccxml", "<PERSON><PERSON>", "checkstyle", "chpl", "cirru", "cjsx", "clixml", "cljc", "cljscm", "cmake", "codecov", "codeql", "cppobjdump", "cproject", "<PERSON><PERSON><PERSON><PERSON>", "dats", "devskim", "ditaval", "dotsettings", "dpatch", "druby", "duby", "dyalog", "eclass", "eclxml", "eliom", "<PERSON><PERSON><PERSON>", "emberscript", "escript", "fancypack", "fcgi", "fshader", "gcode", "gitleaks", "glsl", "glslv", "golo", "grxml", "gshader", "gtpl", "hadolint", "haml", "hlean", "hlsl", "hlsli", "hxsl", "identifer", "ihlp", "ipynb", "irbrc", "irclog", "jbuilder", "jflex", "jscad", "jsfl", "j<PERSON>ld", "jsproj", "keymap", "kicad", "ktlint", "lagda", "lcov", "lidr", "logtalk", "lookml", "lslp", "lvproj", "lych<PERSON><PERSON><PERSON>", "markdownfile", "mata", "matah", "mathematica", "mawk", "maxhelp", "maxpat", "maxproj", "mdpolicy", "minid", "minstars", "mirah", "mkdn", "mkdown", "mkfile", "mkii", "mkiv", "mkvi", "mousemap", "mspec", "mtml", "multiformat", "mupad", "myfile", "nasm", "nawk", "nginxconf", "nlogo", "npmrc", "nproj", "numpy", "numpyw", "numsc", "nvuillam", "objdu<PERSON>", "omgrofl", "opencl", "oxsecurity", "oxygene", "pasm", "phps", "phpt", "plsql", "pluginspec", "pmod", "podsl", "podspec", "prefs", "preid", "prepatch", "purs", "pyde", "pytb", "rabl", "raml", "rbbas", "rbfrm", "rbmnu", "rbres", "rbtbar", "rbuild", "rbuistate", "rbxs", "rdoc", "rebol", "rhtml", "rktd", "rktl", "roff", "rviz", "sagews", "sarif", "sats", "scaml", "scpt", "scrbl", "scxml", "semgrep", "sexp", "shen", "smali", "sparql", "srdf", "ssjs", "s<PERSON><PERSON><PERSON><PERSON><PERSON>l", "sthlp", "ston", "styl", "tcsh", "terrascan", "terrasform", "tflint", "<PERSON><PERSON><PERSON><PERSON>", "traceback", "trivy", "typedoc", "urdf", "vala", "vapi", "vark", "vbhtml", "venv", "vhdl", "vs<PERSON>er", "vssettings", "vxml", "webidl", "weechatlog", "wesh", "wlua", "wsgi", "xacro", "xojo", "xproc", "xproj", "xsjs", "xsjslib", "zcml", "zimpl", "zmpl"]}