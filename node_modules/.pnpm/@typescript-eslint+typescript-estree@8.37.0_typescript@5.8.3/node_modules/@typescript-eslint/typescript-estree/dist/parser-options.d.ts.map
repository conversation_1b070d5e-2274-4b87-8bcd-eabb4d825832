{"version": 3, "file": "parser-options.d.ts", "sourceRoot": "", "sources": ["../src/parser-options.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,oBAAoB,EACpB,UAAU,EACV,gBAAgB,EAChB,qBAAqB,EACrB,UAAU,EACX,MAAM,0BAA0B,CAAC;AAClC,OAAO,KAAK,KAAK,EAAE,MAAM,YAAY,CAAC;AAEtC,OAAO,KAAK,EAAE,QAAQ,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,aAAa,CAAC;AAM/E,UAAU,YAAY;IACpB;;;OAGG;IACH,UAAU,CAAC,EAAE,UAAU,CAAC;IAExB;;;OAGG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAE1B;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;;;;;;;;OASG;IACH,UAAU,CAAC,EAAE,UAAU,CAAC;IAExB;;;OAGG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAEhC;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;;;;;;;OASG;IACH,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IAEpC;;;;;;;;OAQG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IAEd;;;;OAIG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IAOd,QAAQ,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC;IAE/C;;;;OAIG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;IAEhB;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,kCAAkC,CAAC,EAAE,OAAO,CAAC;CAC9C;AAED,UAAU,+BAAgC,SAAQ,YAAY;IAC5D;;;;;;;;OAQG;IACH,aAAa,CAAC,EAAE;QACd;;WAEG;QACH,IAAI,CAAC,EAAE,oBAAoB,CAAC;KAC7B,CAAC;IAEF;;;;;;;;;;;;;;;;;;;OAmBG;IACH,mCAAmC,CAAC,EAAE,OAAO,CAAC;IAE9C;;OAEG;IACH,2CAA2C,CAAC,EAAE,OAAO,CAAC;IAEtD;;;;;OAKG;IACH,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC;IAE/B;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;;;;;;;OASG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAE3B;;;;;;;;OAQG;IACH,OAAO,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,MAAM,EAAE,GAAG,IAAI,CAAC;IAE7C;;;;;;OAMG;IACH,uBAAuB,CAAC,EAAE,MAAM,EAAE,CAAC;IAEnC;;OAEG;IACH,cAAc,CAAC,EAAE,OAAO,GAAG,qBAAqB,CAAC;IAEjD;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;CAChC;AAED,MAAM,MAAM,eAAe,GAAG,+BAA+B,CAAC;AAI9D,MAAM,WAAW,aAAa,CAAC,GAAG,EAAE,SAAS;IAG3C,GAAG,CAAC,KAAK,SAAS,SAAS,EAAE,GAAG,EAAE,GAAG,GAAG,KAAK,CAAC;IAC9C,GAAG,CAAC,GAAG,EAAE,OAAO,GAAG,OAAO,CAAC;CAC5B;AAED,MAAM,WAAW,2BAA2B,CAC1C,GAAG,SAAS,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;IAEzC,GAAG,CAAC,OAAO,SAAS,GAAG,EAAE,GAAG,EAAE,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAClE,GAAG,CAAC,GAAG,EAAE,OAAO,GAAG,OAAO,CAAC;CAC5B;AAED,MAAM,WAAW,kBAAkB;IACjC,qBAAqB,EAAE,OAAO,GAAG,SAAS,CAAC;IAC3C,sBAAsB,EAAE,OAAO,GAAG,SAAS,CAAC;IAC5C,oBAAoB,EAAE,OAAO,GAAG,SAAS,CAAC;CAC3C;AACD,MAAM,WAAW,sBAAsB;IACrC,qBAAqB,EAAE,2BAA2B,CAAC;IACnD,qBAAqB,EAAE,aAAa,CAAC,MAAM,GAAG,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;CACvE;AACD,MAAM,WAAW,iCACf,SAAQ,sBAAsB,EAC5B,kBAAkB;IACpB,mBAAmB,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,KAAK,EAAE,CAAC,MAAM,GAAG,SAAS,CAAC;IACpE,iBAAiB,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,CAAC;IACpD,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC;CACrB;AACD,MAAM,WAAW,oCACf,SAAQ,sBAAsB,EAC5B,kBAAkB;IACpB,OAAO,EAAE,IAAI,CAAC;CACf;AACD,MAAM,MAAM,cAAc,GACtB,oCAAoC,GACpC,iCAAiC,CAAC"}