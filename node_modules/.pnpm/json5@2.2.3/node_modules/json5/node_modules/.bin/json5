#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/project/ai-auto-test/node_modules/.pnpm/json5@2.2.3/node_modules/json5/lib/node_modules:/Users/<USER>/project/ai-auto-test/node_modules/.pnpm/json5@2.2.3/node_modules/json5/node_modules:/Users/<USER>/project/ai-auto-test/node_modules/.pnpm/json5@2.2.3/node_modules:/Users/<USER>/project/ai-auto-test/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/project/ai-auto-test/node_modules/.pnpm/json5@2.2.3/node_modules/json5/lib/node_modules:/Users/<USER>/project/ai-auto-test/node_modules/.pnpm/json5@2.2.3/node_modules/json5/node_modules:/Users/<USER>/project/ai-auto-test/node_modules/.pnpm/json5@2.2.3/node_modules:/Users/<USER>/project/ai-auto-test/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../lib/cli.js" "$@"
else
  exec node  "$basedir/../../lib/cli.js" "$@"
fi
