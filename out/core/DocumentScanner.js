"use strict";
/**
 * 文档扫描器
 *
 * 负责扫描指定目录中的测试相关文档，识别和分组测试文件
 * 支持监听目录变化并自动更新文档列表
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentScanner = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const types_1 = require("../types");
class DocumentScanner {
    constructor() {
        this.watchers = new Map();
    }
    /**
     * 扫描指定目录中的测试文档
     * @param directoryPath 要扫描的目录路径
     * @returns 测试文档列表
     */
    async scanDirectory(directoryPath) {
        try {
            console.log(`开始扫描目录: ${directoryPath}`);
            // 验证目录是否存在
            if (!fs.existsSync(directoryPath)) {
                throw new types_1.PluginError(`目录不存在: ${directoryPath}`, {
                    type: types_1.ErrorType.FILE_SYSTEM_ERROR
                });
            }
            // 递归获取所有文件
            const allFiles = await this.getAllFiles(directoryPath);
            // 过滤出相关文件
            const relevantFiles = allFiles.filter(file => {
                const ext = path.extname(file);
                const basename = path.basename(file);
                return ext === '.md' || ext === '.yaml' || basename.endsWith('.result.md');
            });
            // 按文件名分组
            const groupedDocuments = this.groupDocumentsByName(relevantFiles);
            console.log(`扫描完成，发现 ${groupedDocuments.length} 个测试文档组`);
            return groupedDocuments;
        }
        catch (error) {
            console.error('扫描目录失败:', error);
            throw error;
        }
    }
    /**
     * 监听目录变化
     * @param directoryPath 要监听的目录路径
     * @param callback 变化回调函数
     */
    watchDirectory(directoryPath, callback) {
        // 如果已经在监听该目录，先停止监听
        if (this.watchers.has(directoryPath)) {
            this.watchers.get(directoryPath)?.dispose();
        }
        // 创建文件监听器
        const pattern = new vscode.RelativePattern(directoryPath, '**/*.{md,yaml}');
        const watcher = vscode.workspace.createFileSystemWatcher(pattern);
        // 监听文件变化事件
        watcher.onDidCreate(async () => {
            const docs = await this.scanDirectory(directoryPath);
            callback(docs);
        });
        watcher.onDidDelete(async () => {
            const docs = await this.scanDirectory(directoryPath);
            callback(docs);
        });
        watcher.onDidChange(async () => {
            const docs = await this.scanDirectory(directoryPath);
            callback(docs);
        });
        this.watchers.set(directoryPath, watcher);
        console.log(`开始监听目录: ${directoryPath}`);
    }
    /**
     * 停止监听指定目录
     * @param directoryPath 目录路径
     */
    stopWatching(directoryPath) {
        const watcher = this.watchers.get(directoryPath);
        if (watcher) {
            watcher.dispose();
            this.watchers.delete(directoryPath);
            console.log(`停止监听目录: ${directoryPath}`);
        }
    }
    /**
     * 停止所有监听
     */
    stopAllWatching() {
        for (const [path, watcher] of this.watchers) {
            watcher.dispose();
            console.log(`停止监听目录: ${path}`);
        }
        this.watchers.clear();
    }
    /**
     * 递归获取目录中的所有文件
     * @param dirPath 目录路径
     * @returns 文件路径列表
     */
    async getAllFiles(dirPath) {
        const files = [];
        const items = fs.readdirSync(dirPath);
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            if (stat.isDirectory()) {
                // 递归扫描子目录
                const subFiles = await this.getAllFiles(fullPath);
                files.push(...subFiles);
            }
            else if (stat.isFile()) {
                files.push(fullPath);
            }
        }
        return files;
    }
    /**
     * 按文件名分组文档
     * @param files 文件路径列表
     * @returns 分组后的测试文档列表
     */
    groupDocumentsByName(files) {
        const groups = new Map();
        for (const file of files) {
            const dir = path.dirname(file);
            const basename = path.basename(file);
            const ext = path.extname(file);
            let name;
            let docType;
            if (basename.endsWith('.result.md')) {
                // 结果文档
                name = basename.replace('.result.md', '');
                docType = 'result';
            }
            else if (ext === '.yaml') {
                // 步骤文档
                name = basename.replace('.yaml', '');
                docType = 'step';
            }
            else if (ext === '.md') {
                // 测试文档
                name = basename.replace('.md', '');
                docType = 'test';
            }
            else {
                continue; // 跳过不相关的文件
            }
            // 创建或获取文档组
            const key = `${dir}/${name}`;
            if (!groups.has(key)) {
                groups.set(key, {
                    name,
                    basePath: dir,
                    status: types_1.TestDocumentStatus.MISSING_TEST
                });
            }
            const doc = groups.get(key);
            // 设置对应的文档路径
            switch (docType) {
                case 'test':
                    doc.testDoc = file;
                    break;
                case 'step':
                    doc.stepDoc = file;
                    break;
                case 'result':
                    doc.resultDoc = file;
                    break;
            }
        }
        // 更新文档状态
        for (const doc of groups.values()) {
            if (doc.testDoc && doc.stepDoc) {
                doc.status = types_1.TestDocumentStatus.COMPLETE;
            }
            else if (doc.testDoc && !doc.stepDoc) {
                doc.status = types_1.TestDocumentStatus.MISSING_STEPS;
            }
            else {
                doc.status = types_1.TestDocumentStatus.MISSING_TEST;
            }
        }
        return Array.from(groups.values());
    }
}
exports.DocumentScanner = DocumentScanner;
//# sourceMappingURL=DocumentScanner.js.map