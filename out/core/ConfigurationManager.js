"use strict";
/**
 * 配置管理器
 *
 * 负责管理插件的所有配置项，包括AI服务配置和测试配置
 * 使用VS Code的Settings API进行配置的读取和写入
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurationManager = void 0;
const vscode = __importStar(require("vscode"));
const types_1 = require("../types");
class ConfigurationManager {
    /**
     * 获取AI服务配置
     * @returns AI配置对象
     */
    getAIConfig() {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        return {
            apiUrl: config.get('ai.apiUrl', 'https://api.openai.com/v1'),
            apiKey: config.get('ai.apiKey', ''),
            model: config.get('ai.model', 'gpt-3.5-turbo'),
            timeout: config.get('ai.timeout', 30000)
        };
    }
    /**
     * 设置AI服务配置
     * @param aiConfig AI配置对象
     */
    async setAIConfig(aiConfig) {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        try {
            await config.update('ai.apiUrl', aiConfig.apiUrl, vscode.ConfigurationTarget.Global);
            await config.update('ai.apiKey', aiConfig.apiKey, vscode.ConfigurationTarget.Global);
            await config.update('ai.model', aiConfig.model, vscode.ConfigurationTarget.Global);
            if (aiConfig.timeout !== undefined) {
                await config.update('ai.timeout', aiConfig.timeout, vscode.ConfigurationTarget.Global);
            }
        }
        catch (error) {
            throw new types_1.PluginError('设置AI配置失败', {
                type: types_1.ErrorType.CONFIGURATION_ERROR,
                originalError: error
            });
        }
    }
    /**
     * 获取测试配置
     * @returns 测试配置对象
     */
    getTestConfig() {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        return {
            testDirectory: config.get('test.directory', './tests'),
            autoScan: config.get('test.autoScan', true),
            maxConcurrentTests: config.get('test.maxConcurrentTests', 3)
        };
    }
    /**
     * 设置测试配置
     * @param testConfig 测试配置对象
     */
    async setTestConfig(testConfig) {
        const config = vscode.workspace.getConfiguration(ConfigurationManager.CONFIG_SECTION);
        try {
            await config.update('test.directory', testConfig.testDirectory, vscode.ConfigurationTarget.Workspace);
            await config.update('test.autoScan', testConfig.autoScan, vscode.ConfigurationTarget.Workspace);
            await config.update('test.maxConcurrentTests', testConfig.maxConcurrentTests, vscode.ConfigurationTarget.Workspace);
        }
        catch (error) {
            throw new types_1.PluginError('设置测试配置失败', {
                type: types_1.ErrorType.CONFIGURATION_ERROR,
                originalError: error
            });
        }
    }
    /**
     * 验证配置的有效性
     * @returns 配置是否有效
     */
    async validateConfig() {
        try {
            const aiConfig = this.getAIConfig();
            const testConfig = this.getTestConfig();
            // 验证AI配置
            if (!aiConfig.apiUrl || !aiConfig.apiKey || !aiConfig.model) {
                return false;
            }
            // 验证URL格式
            try {
                new URL(aiConfig.apiUrl);
            }
            catch {
                return false;
            }
            // 验证测试目录
            if (!testConfig.testDirectory) {
                return false;
            }
            // 验证并发数量
            if (testConfig.maxConcurrentTests < 1 || testConfig.maxConcurrentTests > 10) {
                return false;
            }
            return true;
        }
        catch (error) {
            console.error('配置验证失败:', error);
            return false;
        }
    }
    /**
     * 重新加载配置
     * 当配置发生变化时调用
     */
    reloadConfiguration() {
        console.log('重新加载配置...');
        // 这里可以添加配置重新加载后的处理逻辑
        // 例如重新初始化服务、更新UI等
    }
}
exports.ConfigurationManager = ConfigurationManager;
ConfigurationManager.CONFIG_SECTION = 'ai-auto-test';
//# sourceMappingURL=ConfigurationManager.js.map