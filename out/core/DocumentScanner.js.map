{"version": 3, "file": "DocumentScanner.js", "sourceRoot": "", "sources": ["../../src/core/DocumentScanner.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,2CAA6B;AAC7B,uCAAyB;AACzB,oCAAoF;AAEpF,MAAa,eAAe;IAA5B;QACY,aAAQ,GAA0C,IAAI,GAAG,EAAE,CAAC;IAmMxE,CAAC;IAjMG;;;;OAIG;IACI,KAAK,CAAC,aAAa,CAAC,aAAqB;QAC5C,IAAI,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,WAAW,aAAa,EAAE,CAAC,CAAC;YAExC,WAAW;YACX,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;gBAChC,MAAM,IAAI,mBAAW,CAAC,UAAU,aAAa,EAAE,EAAE;oBAC7C,IAAI,EAAE,iBAAS,CAAC,iBAAiB;iBACpC,CAAC,CAAC;YACP,CAAC;YAED,WAAW;YACX,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;YAEvD,UAAU;YACV,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBACzC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACrC,OAAO,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC/E,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAElE,OAAO,CAAC,GAAG,CAAC,WAAW,gBAAgB,CAAC,MAAM,SAAS,CAAC,CAAC;YACzD,OAAO,gBAAgB,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAChC,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,aAAqB,EAAE,QAAwC;QACjF,mBAAmB;QACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,OAAO,EAAE,CAAC;QAChD,CAAC;QAED,UAAU;QACV,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAC5E,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAElE,WAAW;QACX,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;YAC3B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YACrD,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;YAC3B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YACrD,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;YAC3B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YACrD,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,WAAW,aAAa,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,aAAqB;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACjD,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,WAAW,aAAa,EAAE,CAAC,CAAC;QAC5C,CAAC;IACL,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1C,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,WAAW,CAAC,OAAe;QACrC,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEtC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAC1C,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;gBACrB,UAAU;gBACV,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAClD,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;YAC5B,CAAC;iBAAM,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;gBACvB,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACzB,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,KAAe;QACvC,MAAM,MAAM,GAAG,IAAI,GAAG,EAAwB,CAAC;QAE/C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAE/B,IAAI,IAAY,CAAC;YACjB,IAAI,OAAmC,CAAC;YAExC,IAAI,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAClC,OAAO;gBACP,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;gBAC1C,OAAO,GAAG,QAAQ,CAAC;YACvB,CAAC;iBAAM,IAAI,GAAG,KAAK,OAAO,EAAE,CAAC;gBACzB,OAAO;gBACP,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBACrC,OAAO,GAAG,MAAM,CAAC;YACrB,CAAC;iBAAM,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;gBACvB,OAAO;gBACP,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;gBACnC,OAAO,GAAG,MAAM,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACJ,SAAS,CAAC,WAAW;YACzB,CAAC;YAED,WAAW;YACX,MAAM,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACnB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE;oBACZ,IAAI;oBACJ,QAAQ,EAAE,GAAG;oBACb,MAAM,EAAE,0BAAkB,CAAC,YAAY;iBAC1C,CAAC,CAAC;YACP,CAAC;YAED,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC;YAE7B,YAAY;YACZ,QAAQ,OAAO,EAAE,CAAC;gBACd,KAAK,MAAM;oBACP,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;oBACnB,MAAM;gBACV,KAAK,MAAM;oBACP,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC;oBACnB,MAAM;gBACV,KAAK,QAAQ;oBACT,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC;oBACrB,MAAM;YACd,CAAC;QACL,CAAC;QAED,SAAS;QACT,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC;YAChC,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;gBAC7B,GAAG,CAAC,MAAM,GAAG,0BAAkB,CAAC,QAAQ,CAAC;YAC7C,CAAC;iBAAM,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;gBACrC,GAAG,CAAC,MAAM,GAAG,0BAAkB,CAAC,aAAa,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACJ,GAAG,CAAC,MAAM,GAAG,0BAAkB,CAAC,YAAY,CAAC;YACjD,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IACvC,CAAC;CACJ;AApMD,0CAoMC"}