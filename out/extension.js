"use strict";
/**
 * AI Auto Test VS Code 插件主入口文件
 *
 * 该文件是VS Code插件的主要入口点，负责：
 * 1. 插件的激活和停用
 * 2. 注册所有命令和事件处理器
 * 3. 初始化核心服务组件
 * 4. 管理插件的生命周期
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const AIServiceClient_1 = require("./core/AIServiceClient");
const ConfigurationManager_1 = require("./core/ConfigurationManager");
const DocumentScanner_1 = require("./core/DocumentScanner");
const TestExecutionEngine_1 = require("./core/TestExecutionEngine");
const UIController_1 = require("./ui/UIController");
/**
 * 插件激活函数
 * 当插件被激活时调用，负责初始化所有组件和注册命令
 *
 * @param context VS Code扩展上下文
 */
function activate(context) {
    console.log('AI Auto Test 插件正在激活...');
    // 初始化核心组件
    const configManager = new ConfigurationManager_1.ConfigurationManager();
    const documentScanner = new DocumentScanner_1.DocumentScanner();
    const aiServiceClient = new AIServiceClient_1.AIServiceClient(configManager);
    const testExecutionEngine = new TestExecutionEngine_1.TestExecutionEngine(configManager);
    const uiController = new UIController_1.UIController(context);
    // 暂时使用这些变量以避免ESLint警告，后续任务中会实际使用
    console.log('核心组件已初始化:', {
        aiServiceClient: !!aiServiceClient,
        testExecutionEngine: !!testExecutionEngine
    });
    // 注册命令处理器
    registerCommands(context, {
        configManager,
        documentScanner,
        aiServiceClient,
        testExecutionEngine,
        uiController
    });
    // 监听配置变化
    const configChangeDisposable = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('ai-auto-test')) {
            console.log('AI Auto Test 配置已更改，重新加载配置...');
            configManager.reloadConfiguration();
        }
    });
    // 将所有disposable添加到context中，确保插件停用时正确清理
    context.subscriptions.push(configChangeDisposable);
    console.log('AI Auto Test 插件激活完成');
}
/**
 * 插件停用函数
 * 当插件被停用时调用，负责清理资源
 */
function deactivate() {
    console.log('AI Auto Test 插件正在停用...');
    // 这里可以添加清理逻辑，如关闭连接、保存状态等
}
/**
 * 注册所有VS Code命令
 *
 * @param context VS Code扩展上下文
 * @param services 服务组件集合
 */
function registerCommands(context, services) {
    const { configManager, documentScanner, aiServiceClient, testExecutionEngine, uiController } = services;
    // 注册"打开测试面板"命令
    const openTestPanelCommand = vscode.commands.registerCommand('ai-auto-test.openTestPanel', async () => {
        try {
            console.log('打开测试面板...');
            await uiController.showTestPanel();
            // 自动扫描测试目录
            const testConfig = configManager.getTestConfig();
            if (testConfig.autoScan && testConfig.testDirectory) {
                const documents = await documentScanner.scanDirectory(testConfig.testDirectory);
                await uiController.updateTestList(documents);
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`打开测试面板失败: ${error}`);
            console.error('打开测试面板失败:', error);
        }
    });
    // 注册"打开配置面板"命令
    const openConfigPanelCommand = vscode.commands.registerCommand('ai-auto-test.openConfigPanel', async () => {
        try {
            console.log('打开配置面板...');
            await uiController.showConfigPanel();
        }
        catch (error) {
            vscode.window.showErrorMessage(`打开配置面板失败: ${error}`);
            console.error('打开配置面板失败:', error);
        }
    });
    // 注册"扫描测试目录"命令
    const scanTestDirectoryCommand = vscode.commands.registerCommand('ai-auto-test.scanTestDirectory', async (uri) => {
        try {
            console.log('扫描测试目录...');
            // 确定要扫描的目录
            let targetDirectory;
            if (uri && uri.fsPath) {
                targetDirectory = uri.fsPath;
            }
            else {
                const testConfig = configManager.getTestConfig();
                targetDirectory = testConfig.testDirectory;
            }
            if (!targetDirectory) {
                vscode.window.showWarningMessage('请先配置测试目录');
                return;
            }
            // 执行扫描
            const documents = await documentScanner.scanDirectory(targetDirectory);
            vscode.window.showInformationMessage(`扫描完成，发现 ${documents.length} 个测试文档组`);
            // 更新UI
            await uiController.updateTestList(documents);
        }
        catch (error) {
            vscode.window.showErrorMessage(`扫描测试目录失败: ${error}`);
            console.error('扫描测试目录失败:', error);
        }
    });
    // 注册"生成测试步骤"命令
    const generateStepsCommand = vscode.commands.registerCommand('ai-auto-test.generateSteps', async () => {
        try {
            console.log('生成测试步骤...');
            // 检查AI服务连接
            const isConnected = await aiServiceClient.validateConnection();
            if (!isConnected) {
                vscode.window.showWarningMessage('AI服务连接失败，请检查配置');
                return;
            }
            vscode.window.showInformationMessage('测试步骤生成功能正在开发中...');
            // TODO: 实现步骤生成逻辑
        }
        catch (error) {
            vscode.window.showErrorMessage(`生成测试步骤失败: ${error}`);
            console.error('生成测试步骤失败:', error);
        }
    });
    // 注册"运行测试"命令
    const runTestsCommand = vscode.commands.registerCommand('ai-auto-test.runTests', async () => {
        try {
            console.log('运行测试...');
            // 检查测试执行引擎状态
            console.log('测试执行引擎已准备就绪:', !!testExecutionEngine);
            vscode.window.showInformationMessage('测试运行功能正在开发中...');
            // TODO: 实现测试运行逻辑
        }
        catch (error) {
            vscode.window.showErrorMessage(`运行测试失败: ${error}`);
            console.error('运行测试失败:', error);
        }
    });
    // 将所有命令添加到context中
    context.subscriptions.push(openTestPanelCommand, openConfigPanelCommand, scanTestDirectoryCommand, generateStepsCommand, runTestsCommand);
    console.log('所有命令注册完成');
}
//# sourceMappingURL=extension.js.map