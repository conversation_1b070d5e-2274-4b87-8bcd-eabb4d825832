"use strict";
/**
 * UI控制器
 *
 * 负责管理插件的用户界面，包括WebView面板的创建和消息通信
 * 提供测试面板和配置面板的显示和交互功能
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.UIController = void 0;
const vscode = __importStar(require("vscode"));
const types_1 = require("../types");
class UIController {
    constructor(context) {
        this.context = context;
    }
    /**
     * 显示测试面板
     */
    async showTestPanel() {
        try {
            if (this.testPanel) {
                // 如果面板已存在，直接显示
                this.testPanel.reveal(vscode.ViewColumn.One);
                return;
            }
            // 创建新的WebView面板
            this.testPanel = vscode.window.createWebviewPanel('ai-auto-test.testPanel', 'AI Auto Test - 测试面板', vscode.ViewColumn.One, {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this.context.extensionUri, 'media'),
                    vscode.Uri.joinPath(this.context.extensionUri, 'out', 'webview')
                ]
            });
            // 设置WebView内容
            this.testPanel.webview.html = this.getTestPanelHtml();
            // 监听面板关闭事件
            this.testPanel.onDidDispose(() => {
                this.testPanel = undefined;
            });
            // 监听来自WebView的消息
            this.testPanel.webview.onDidReceiveMessage((message) => {
                this.handleTestPanelMessage(message);
            }, undefined, this.context.subscriptions);
            console.log('测试面板已创建');
        }
        catch (error) {
            console.error('创建测试面板失败:', error);
            throw error;
        }
    }
    /**
     * 显示配置面板
     */
    async showConfigPanel() {
        try {
            if (this.configPanel) {
                // 如果面板已存在，直接显示
                this.configPanel.reveal(vscode.ViewColumn.Two);
                return;
            }
            // 创建新的WebView面板
            this.configPanel = vscode.window.createWebviewPanel('ai-auto-test.configPanel', 'AI Auto Test - 配置面板', vscode.ViewColumn.Two, {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this.context.extensionUri, 'media'),
                    vscode.Uri.joinPath(this.context.extensionUri, 'out', 'webview')
                ]
            });
            // 设置WebView内容
            this.configPanel.webview.html = this.getConfigPanelHtml();
            // 监听面板关闭事件
            this.configPanel.onDidDispose(() => {
                this.configPanel = undefined;
            });
            // 监听来自WebView的消息
            this.configPanel.webview.onDidReceiveMessage((message) => {
                this.handleConfigPanelMessage(message);
            }, undefined, this.context.subscriptions);
            console.log('配置面板已创建');
        }
        catch (error) {
            console.error('创建配置面板失败:', error);
            throw error;
        }
    }
    /**
     * 更新测试列表
     * @param documents 测试文档列表
     */
    async updateTestList(documents) {
        if (!this.testPanel) {
            return;
        }
        const testListItems = documents.map(doc => ({
            document: doc,
            selected: false,
            running: false
        }));
        const message = {
            type: types_1.UIMessageType.UPDATE_TEST_LIST,
            data: testListItems,
            timestamp: Date.now()
        };
        await this.testPanel.webview.postMessage(message);
        console.log(`已更新测试列表，共 ${documents.length} 项`);
    }
    /**
     * 显示测试进度
     * @param testName 测试名称
     * @param progress 进度百分比
     */
    showProgress(testName, progress) {
        if (!this.testPanel) {
            return;
        }
        const message = {
            type: types_1.UIMessageType.SHOW_PROGRESS,
            data: { testName, progress },
            timestamp: Date.now()
        };
        this.testPanel.webview.postMessage(message);
    }
    /**
     * 显示测试结果
     * @param results 测试结果列表
     */
    showResults(results) {
        if (!this.testPanel) {
            return;
        }
        const message = {
            type: types_1.UIMessageType.SHOW_RESULTS,
            data: results,
            timestamp: Date.now()
        };
        this.testPanel.webview.postMessage(message);
        console.log(`已显示 ${results.length} 个测试结果`);
    }
    /**
     * 处理测试面板消息
     * @param message UI消息
     */
    handleTestPanelMessage(message) {
        console.log('收到测试面板消息:', message);
        // TODO: 实现消息处理逻辑
    }
    /**
     * 处理配置面板消息
     * @param message UI消息
     */
    handleConfigPanelMessage(message) {
        console.log('收到配置面板消息:', message);
        // TODO: 实现消息处理逻辑
    }
    /**
     * 获取测试面板HTML内容
     * @returns HTML字符串
     */
    getTestPanelHtml() {
        return `
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AI Auto Test - 测试面板</title>
                <style>
                    body { font-family: var(--vscode-font-family); padding: 20px; }
                    .test-list { margin-top: 20px; }
                    .test-item { padding: 10px; border: 1px solid var(--vscode-panel-border); margin-bottom: 10px; }
                    .test-item.selected { background-color: var(--vscode-list-activeSelectionBackground); }
                    .status { font-weight: bold; }
                    .status.complete { color: var(--vscode-testing-iconPassed); }
                    .status.missing-steps { color: var(--vscode-testing-iconQueued); }
                    .status.missing-test { color: var(--vscode-testing-iconFailed); }
                </style>
            </head>
            <body>
                <h1>AI Auto Test - 测试面板</h1>
                <div>
                    <button id="scanBtn">扫描测试目录</button>
                    <button id="generateBtn">生成步骤</button>
                    <button id="runBtn">运行测试</button>
                </div>
                <div id="testList" class="test-list">
                    <p>暂无测试文档，请先扫描测试目录</p>
                </div>
                <script>
                    const vscode = acquireVsCodeApi();
                    
                    // 监听按钮点击事件
                    document.getElementById('scanBtn').addEventListener('click', () => {
                        vscode.postMessage({ type: 'scan', data: {} });
                    });
                    
                    document.getElementById('generateBtn').addEventListener('click', () => {
                        vscode.postMessage({ type: 'generate', data: {} });
                    });
                    
                    document.getElementById('runBtn').addEventListener('click', () => {
                        vscode.postMessage({ type: 'run', data: {} });
                    });
                    
                    // 监听来自扩展的消息
                    window.addEventListener('message', event => {
                        const message = event.data;
                        if (message.type === 'updateTestList') {
                            updateTestList(message.data);
                        }
                    });
                    
                    function updateTestList(testItems) {
                        const listElement = document.getElementById('testList');
                        if (testItems.length === 0) {
                            listElement.innerHTML = '<p>暂无测试文档，请先扫描测试目录</p>';
                            return;
                        }
                        
                        const html = testItems.map(item => {
                            const doc = item.document;
                            return \`
                                <div class="test-item">
                                    <h3>\${doc.name}</h3>
                                    <p>路径: \${doc.basePath}</p>
                                    <p>状态: <span class="status \${doc.status}">\${getStatusText(doc.status)}</span></p>
                                </div>
                            \`;
                        }).join('');
                        
                        listElement.innerHTML = html;
                    }
                    
                    function getStatusText(status) {
                        switch (status) {
                            case 'complete': return '完整';
                            case 'missing-steps': return '缺少步骤文档';
                            case 'missing-test': return '缺少测试文档';
                            default: return '未知';
                        }
                    }
                </script>
            </body>
            </html>
        `;
    }
    /**
     * 获取配置面板HTML内容
     * @returns HTML字符串
     */
    getConfigPanelHtml() {
        return `
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AI Auto Test - 配置面板</title>
                <style>
                    body { font-family: var(--vscode-font-family); padding: 20px; }
                    .config-section { margin-bottom: 30px; }
                    .form-group { margin-bottom: 15px; }
                    label { display: block; margin-bottom: 5px; font-weight: bold; }
                    input, select { width: 100%; padding: 8px; border: 1px solid var(--vscode-input-border); }
                    button { padding: 10px 20px; margin-right: 10px; }
                </style>
            </head>
            <body>
                <h1>AI Auto Test - 配置面板</h1>
                
                <div class="config-section">
                    <h2>AI服务配置</h2>
                    <div class="form-group">
                        <label for="apiUrl">API地址:</label>
                        <input type="text" id="apiUrl" placeholder="https://api.openai.com/v1">
                    </div>
                    <div class="form-group">
                        <label for="apiKey">API密钥:</label>
                        <input type="password" id="apiKey" placeholder="输入您的API密钥">
                    </div>
                    <div class="form-group">
                        <label for="model">模型:</label>
                        <select id="model">
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="gpt-4">GPT-4</option>
                        </select>
                    </div>
                </div>
                
                <div class="config-section">
                    <h2>测试配置</h2>
                    <div class="form-group">
                        <label for="testDirectory">测试目录:</label>
                        <input type="text" id="testDirectory" placeholder="./tests">
                    </div>
                    <div class="form-group">
                        <label for="autoScan">自动扫描:</label>
                        <input type="checkbox" id="autoScan" checked>
                    </div>
                    <div class="form-group">
                        <label for="maxConcurrentTests">最大并发数:</label>
                        <input type="number" id="maxConcurrentTests" value="3" min="1" max="10">
                    </div>
                </div>
                
                <div>
                    <button id="saveBtn">保存配置</button>
                    <button id="testBtn">测试连接</button>
                </div>
                
                <script>
                    const vscode = acquireVsCodeApi();
                    
                    document.getElementById('saveBtn').addEventListener('click', () => {
                        const config = {
                            ai: {
                                apiUrl: document.getElementById('apiUrl').value,
                                apiKey: document.getElementById('apiKey').value,
                                model: document.getElementById('model').value
                            },
                            test: {
                                testDirectory: document.getElementById('testDirectory').value,
                                autoScan: document.getElementById('autoScan').checked,
                                maxConcurrentTests: parseInt(document.getElementById('maxConcurrentTests').value)
                            }
                        };
                        
                        vscode.postMessage({ type: 'saveConfig', data: config });
                    });
                    
                    document.getElementById('testBtn').addEventListener('click', () => {
                        vscode.postMessage({ type: 'testConnection', data: {} });
                    });
                </script>
            </body>
            </html>
        `;
    }
}
exports.UIController = UIController;
//# sourceMappingURL=UIController.js.map