"use strict";
/**
 * AI Auto Test 插件的核心类型定义
 *
 * 该文件包含了插件中使用的所有接口、类型和枚举定义
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PluginError = exports.ErrorType = exports.UIMessageType = exports.TestExecutionStatus = exports.TestDocumentStatus = void 0;
/**
 * 测试文档状态枚举
 */
var TestDocumentStatus;
(function (TestDocumentStatus) {
    /** 完整：包含测试文档、步骤文档和结果文档 */
    TestDocumentStatus["COMPLETE"] = "complete";
    /** 缺少步骤文档 */
    TestDocumentStatus["MISSING_STEPS"] = "missing-steps";
    /** 缺少测试文档 */
    TestDocumentStatus["MISSING_TEST"] = "missing-test";
})(TestDocumentStatus || (exports.TestDocumentStatus = TestDocumentStatus = {}));
/**
 * 测试执行状态枚举
 */
var TestExecutionStatus;
(function (TestExecutionStatus) {
    /** 未开始 */
    TestExecutionStatus["NOT_STARTED"] = "not-started";
    /** 执行中 */
    TestExecutionStatus["RUNNING"] = "running";
    /** 已完成 */
    TestExecutionStatus["COMPLETED"] = "completed";
    /** 已失败 */
    TestExecutionStatus["FAILED"] = "failed";
    /** 已取消 */
    TestExecutionStatus["CANCELLED"] = "cancelled";
})(TestExecutionStatus || (exports.TestExecutionStatus = TestExecutionStatus = {}));
/**
 * UI消息类型枚举
 */
var UIMessageType;
(function (UIMessageType) {
    /** 更新测试列表 */
    UIMessageType["UPDATE_TEST_LIST"] = "updateTestList";
    /** 更新配置 */
    UIMessageType["UPDATE_CONFIG"] = "updateConfig";
    /** 显示进度 */
    UIMessageType["SHOW_PROGRESS"] = "showProgress";
    /** 显示结果 */
    UIMessageType["SHOW_RESULTS"] = "showResults";
    /** 显示错误 */
    UIMessageType["SHOW_ERROR"] = "showError";
    /** 用户操作 */
    UIMessageType["USER_ACTION"] = "userAction";
})(UIMessageType || (exports.UIMessageType = UIMessageType = {}));
/**
 * 错误类型枚举
 */
var ErrorType;
(function (ErrorType) {
    /** 配置错误 */
    ErrorType["CONFIGURATION_ERROR"] = "configuration-error";
    /** AI服务错误 */
    ErrorType["AI_SERVICE_ERROR"] = "ai-service-error";
    /** MCP连接错误 */
    ErrorType["MCP_CONNECTION_ERROR"] = "mcp-connection-error";
    /** 文件系统错误 */
    ErrorType["FILE_SYSTEM_ERROR"] = "file-system-error";
    /** 测试执行错误 */
    ErrorType["TEST_EXECUTION_ERROR"] = "test-execution-error";
    /** 未知错误 */
    ErrorType["UNKNOWN_ERROR"] = "unknown-error";
})(ErrorType || (exports.ErrorType = ErrorType = {}));
/**
 * 自定义插件错误类
 */
class PluginError extends Error {
    constructor(message, options) {
        super(message);
        this.name = 'PluginError';
        this.type = options.type;
        this.code = options.code;
        this.originalError = options.originalError;
        this.context = options.context;
    }
}
exports.PluginError = PluginError;
//# sourceMappingURL=index.js.map