/**
 * AI Auto Test 插件的核心类型定义
 *
 * 该文件包含了插件中使用的所有接口、类型和枚举定义
 */
/**
 * AI服务配置接口
 */
export interface AIConfiguration {
    /** API服务地址 */
    apiUrl: string;
    /** API密钥 */
    apiKey: string;
    /** 使用的AI模型 */
    model: string;
    /** 请求超时时间（毫秒） */
    timeout?: number;
}
/**
 * 测试配置接口
 */
export interface TestConfiguration {
    /** 测试文件目录路径 */
    testDirectory: string;
    /** 是否自动扫描目录 */
    autoScan: boolean;
    /** 最大并发测试数量 */
    maxConcurrentTests: number;
}
/**
 * 测试文档状态枚举
 */
export declare enum TestDocumentStatus {
    /** 完整：包含测试文档、步骤文档和结果文档 */
    COMPLETE = "complete",
    /** 缺少步骤文档 */
    MISSING_STEPS = "missing-steps",
    /** 缺少测试文档 */
    MISSING_TEST = "missing-test"
}
/**
 * 测试文档接口
 * 表示一组相关的测试文件（测试文档、步骤文档、结果文档）
 */
export interface TestDocument {
    /** 测试名称（基于文件名） */
    name: string;
    /** 基础路径 */
    basePath: string;
    /** 测试文档路径（*.md） */
    testDoc?: string;
    /** 步骤文档路径（*.yaml） */
    stepDoc?: string;
    /** 结果文档路径（*.result.md） */
    resultDoc?: string;
    /** 文档状态 */
    status: TestDocumentStatus;
}
/**
 * 步骤生成请求接口
 */
export interface StepGenerationRequest {
    /** 测试文档内容 */
    testContent: string;
    /** 测试名称 */
    testName: string;
    /** 额外上下文信息 */
    context?: string;
}
/**
 * 生成的测试步骤接口
 */
export interface GeneratedStep {
    /** 操作类型 */
    action: string;
    /** CSS选择器 */
    selector?: string;
    /** 输入值 */
    value?: string;
    /** 断言类型 */
    assertion?: string;
    /** 步骤描述 */
    description: string;
    /** 等待时间（毫秒） */
    waitTime?: number;
    /** 是否可选步骤 */
    optional?: boolean;
}
/**
 * 测试执行状态枚举
 */
export declare enum TestExecutionStatus {
    /** 未开始 */
    NOT_STARTED = "not-started",
    /** 执行中 */
    RUNNING = "running",
    /** 已完成 */
    COMPLETED = "completed",
    /** 已失败 */
    FAILED = "failed",
    /** 已取消 */
    CANCELLED = "cancelled"
}
/**
 * 步骤执行结果接口
 */
export interface StepResult {
    /** 执行的步骤 */
    step: GeneratedStep;
    /** 执行状态 */
    status: 'passed' | 'failed' | 'skipped';
    /** 执行耗时（毫秒） */
    duration: number;
    /** 错误信息 */
    error?: string;
    /** 截图路径 */
    screenshot?: string;
    /** 开始时间 */
    startTime: Date;
    /** 结束时间 */
    endTime?: Date;
}
/**
 * 测试结果接口
 */
export interface TestResult {
    /** 测试名称 */
    testName: string;
    /** 测试状态 */
    status: 'passed' | 'failed' | 'skipped';
    /** 总耗时（毫秒） */
    duration: number;
    /** 步骤执行结果列表 */
    steps: StepResult[];
    /** 错误信息 */
    error?: string;
    /** 截图列表 */
    screenshots?: string[];
    /** 开始时间 */
    startTime: Date;
    /** 结束时间 */
    endTime?: Date;
    /** 测试环境信息 */
    environment?: {
        browser?: string;
        viewport?: {
            width: number;
            height: number;
        };
        userAgent?: string;
    };
}
/**
 * 测试列表项接口
 * 用于UI显示的测试项
 */
export interface TestListItem {
    /** 测试文档 */
    document: TestDocument;
    /** 是否被选中 */
    selected: boolean;
    /** 是否正在运行 */
    running: boolean;
    /** 最后一次测试结果 */
    lastResult?: TestResult;
    /** 执行进度（0-100） */
    progress?: number;
}
/**
 * UI消息类型枚举
 */
export declare enum UIMessageType {
    /** 更新测试列表 */
    UPDATE_TEST_LIST = "updateTestList",
    /** 更新配置 */
    UPDATE_CONFIG = "updateConfig",
    /** 显示进度 */
    SHOW_PROGRESS = "showProgress",
    /** 显示结果 */
    SHOW_RESULTS = "showResults",
    /** 显示错误 */
    SHOW_ERROR = "showError",
    /** 用户操作 */
    USER_ACTION = "userAction"
}
/**
 * UI消息接口
 */
export interface UIMessage {
    /** 消息类型 */
    type: UIMessageType;
    /** 消息数据 */
    data: unknown;
    /** 消息ID */
    id?: string;
    /** 时间戳 */
    timestamp?: number;
}
/**
 * MCP连接配置接口
 */
export interface MCPConnectionConfig {
    /** MCP服务地址 */
    serverUrl: string;
    /** 连接超时时间 */
    timeout: number;
    /** 重试次数 */
    retryCount: number;
    /** 重试间隔（毫秒） */
    retryInterval: number;
}
/**
 * 错误类型枚举
 */
export declare enum ErrorType {
    /** 配置错误 */
    CONFIGURATION_ERROR = "configuration-error",
    /** AI服务错误 */
    AI_SERVICE_ERROR = "ai-service-error",
    /** MCP连接错误 */
    MCP_CONNECTION_ERROR = "mcp-connection-error",
    /** 文件系统错误 */
    FILE_SYSTEM_ERROR = "file-system-error",
    /** 测试执行错误 */
    TEST_EXECUTION_ERROR = "test-execution-error",
    /** 未知错误 */
    UNKNOWN_ERROR = "unknown-error"
}
/**
 * 插件错误接口
 */
export interface IPluginError extends Error {
    /** 错误类型 */
    type: ErrorType;
    /** 错误代码 */
    code?: string;
    /** 原始错误 */
    originalError?: Error;
    /** 错误上下文 */
    context?: Record<string, unknown>;
}
/**
 * 自定义插件错误类
 */
export declare class PluginError extends Error implements IPluginError {
    type: ErrorType;
    code?: string;
    originalError?: Error;
    context?: Record<string, unknown>;
    constructor(message: string, options: {
        type: ErrorType;
        code?: string;
        originalError?: Error;
        context?: Record<string, unknown>;
    });
}
//# sourceMappingURL=index.d.ts.map